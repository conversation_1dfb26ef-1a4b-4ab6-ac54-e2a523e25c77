<template>
  <div class="agentRelation-page">
    <!-- 搜索 -->
    <div class="searchHeader">
      <el-form :inline="true">
        <el-form-item label="编号">
          <el-input size="mini" placeholder="请输入编号" v-model="query.uid"></el-input>
        </el-form-item>
        <el-form-item label=代理关系>
          <el-select size="mini" v-model="relation_type" placeholder="请选择">
            <el-option
              v-for="item in options"
              :key="item.value"
              :label="item.label"
              :value="item.value">
            </el-option>
          </el-select>
        </el-form-item>
        <el-button size="mini" style="margin-top:2px"  type="primary" @click="search">查询</el-button>
      </el-form>
    </div>

    <!-- 表格 -->
      <el-table
        ref="treeTable"
        v-loading="listLoading"
        :data="list"
        style="width: 100%"
        size="mini"
        row-key="userid"
        border
        lazy
        :load="load"
        :tree-props="{children: 'children', hasChildren: 'hasChildren'}"
        :row-style="{'font-size':'12px','color':'#324253'}" height="auto"
        :header-row-style="{'font-size':'12px','color':'#1f2021'}"
      >
        <el-table-column
          prop="userid"
          label="编号"
        >
        </el-table-column>
        <el-table-column
          prop="type_name"
          label="类型"
        >
        </el-table-column>
        <el-table-column
          prop="nickname"
          label="昵称"
        >
        </el-table-column>
        <el-table-column
          prop="balance"
          label="账户余额"
        >
          <div slot-scope="scope" >
            {{scope.row.balance | Fslice}}
          </div>
        </el-table-column>
        <el-table-column
          label="出售费率"
          align="center"
          width="300"
        >
          <div slot-scope="scope" >
              <span v-for=" (item,index) in scope.row.pay_list " :key="index">
                  {{payTypeRel[item.paytype]}}:{{item.sell_comm_rate*100}}%,
              </span>
          </div>
        </el-table-column>
        <el-table-column
          label="购买费率"
          align="center"
          width="300"
        >
          <div slot-scope="scope" >
              <span v-for=" (item,index) in scope.row.pay_list " :key="index">
                  {{payTypeRel[item.paytype]}}:{{item.buy_comm_rate*100}}%,
              </span>
          </div>
        </el-table-column>
        <el-table-column
          label="代付费率"
          align="center"
          width="300"
        >
          <div slot-scope="scope" >
              <span v-for=" (item,index) in scope.row.pay_list " :key="index">
                  {{payTypeRel[item.paytype]}}:{{item.behalf_buy_comm_rate*100}}%,
              </span>
          </div>
        </el-table-column>
        <!-- <el-table-column
          prop="confNameList"
          label="已配置商户"
          align="center"
          width="200"
        ></el-table-column> -->
        <!-- <el-table-column
          label="操作"
          fixed="right"
          width="200"
          prop="action"
          align="center"
        >
          <template slot-scope="scope" v-if="scope.row.op">
            <el-button
              v-for="(item,index) in scope.row.op"
              :key="index"
              type="text"
              size="small"
              @click="btnClick(scope.row,item)"
            >{{item.btn_name}}</el-button>
          </template>
        </el-table-column> -->
      </el-table>

  </div>
</template>
<script>
export default {
  data() {
    return {
      query: {
        search: 'N',
        uid: '',
      },
      relation_type: 1, // 1查下级  2查上级
      current_agent_type: 1,
      options: [
        { label: '查下级', value: 1 },
        { label: '查上级', value: 2 },
      ],
      listLoading: false, // 表格loading
      list: [],
      payTypeRel: {}
    }
  },
  methods: {
    btnClick(row, item) {
      // if(item.type==1){//设置费率
      //   this.$parent.$refs.SetRate.show(row)
      // }else if(item.type==2){//删除
      //   this.deleteAgent(row)
      // }
    },
    search() {
      this.getList()
    },
    // 查询代理
    async getList() {
      // 查询上级必须传uid
      if (this.relation_type !== 1) {
        if (this.query.uid.trim() === '') {
          this.$message({
            message: '请输入编号查询上级',
            type: 'error'
          });
          return;
        }
      }

      this.$nextTick(async () => {
        this.$refs['treeTable'].doLayout()
        if (this.listLoading) return;
        this.listLoading = true;
        const query = this.query
        

        const res = this.relation_type === 1 ? await this.$http.post('/channel/user/searchSub', query) : await this.$http.post('/channel/user/searchPar', query)
        if (res) {
          const { list, payTypeRel } = res

          list.forEach(v => {
            if (v['haschild'] === 111) {
              v['hasChildren'] = true
            }
          })

          this.list = list || [];
          this.payTypeRel = payTypeRel || {};
          this.listLoading = false;
        }
      })

    },
    async load(tree, treeNode, resolve) {
      console.log(resolve)
      const query = {
        search: 'Y',
        searchID: tree.userid
      }
      const res = this.relation_type === 1 ? await this.$http.post('/channel/user/searchSub', query) : await this.$http.post('/channel/user/searchPar', query)
      if (res) {
        const { list } = res

        list.forEach(v => {
          if (v['haschild'] === 111) {
            v['hasChildren'] = true
          }
        })
        
        resolve(list)
      }
    }
  },
  mounted() {
    this.getList()
  }
}
</script>