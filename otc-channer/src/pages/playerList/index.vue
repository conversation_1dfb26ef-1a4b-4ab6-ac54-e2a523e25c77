<template>
    <div class='container'>
        <Query ref="Query" @getData = " getData "  ></Query>
        <Table ref="Table" :list=" list " @sortChange = " sortChange " @do-hand="doHand"></Table>
        <div class="footer">
            <div class="count" style="display: flex;">
                <div style="marginRight: 10px;">
                  <p> 当前页余额汇总:{{$tool.BigJs(pageSum,1,3,4)}} USDT</p>
                  <p> 账户余额汇总:{{$tool.BigJs(sum,1,3,4)}} USDT</p>
                </div>
                <div>
                  <p> 当前页收益汇总:{{$tool.BigJs(pageIncome,1,3,4)}} USDT</p>
                  <p> 总收益汇总:{{$tool.BigJs(incomeSum,1,3,4)}} USDT</p>
                </div>
            </div> 
            <el-pagination
                @size-change="handleSizeChange"
                @current-change="handleCurrentChange"
                :current-page="filterForm.page"
                :page-size="filterForm.pagesize"
                :total="total*1"
                >
            </el-pagination>
        </div>
        
        <SetRate ref="SetRate"></SetRate>
        <AddPlayer ref="AddPlayer"></AddPlayer>


        <!-- 修改团队名称 -->
        <el-dialog title='修改团队名称'  width='400px' :visible.sync='edit_teamname_show' class='dialog' >
          <div><el-input v-model="teamname" placeholder="请输入团队名称"></el-input></div>
          <div slot='footer' class='dialog-footer'>
            <el-button type=''  size='small' @click='edit_teamname_show=false'>取消</el-button>
            <el-button type='primary'  size='small' @click='editTeamname'>提交</el-button>
          </div>
        </el-dialog>


        <!-- 批量转账 -->
        <el-dialog title='批量转账'  width='600px' :visible.sync='batch_transfer_show' class='dialog' >
          <div>
            <el-row style="textAlign: center;">
              <el-col :span="8">转账数量</el-col>
              <el-col :span="8">TO编码/昵称</el-col>
              <el-col :span="8">操作</el-col>
            </el-row>
            <el-form inline label-width="130px">
              <el-row v-for="(item, index) in form.selectList" :key="item.userid" style="margin: 10px 0; textAlign: center;">
                <el-col :span="8">
                  <el-input v-model="item.transfer_amount" oninput=" value = value.replace(/[^\d.]/g,'').match(/^\d*(\.?\d{0,4})/g)[0] " placeholder="请输入转账数量" size="small"></el-input>
                </el-col>
                <el-col :span="8" style="lineHeight: 40px;">
                  {{item.userid + '/' + item.nickname}}
                </el-col>
                <el-col :span="8">
                  <el-button type="text" @click="deleteSelect(item.userid)">删除</el-button>
                </el-col>
              </el-row>
            </el-form>
          </div>
          <div style="margin: 20px 0;">
            <div style="marginBottom: 5px;">转账总数：<span>{{current_transfer_amount}} USDT</span></div>
            <div>可转余额：<span>{{balance}} USDT</span></div>
          </div>
          <div>
            <el-row>
              <el-col :span="3" style="lineHeight: 30px;">资金密码：</el-col>
              <el-col :span="8">
                <el-input v-model="pwd" type="password" placeholder="请输入资金密码" size="small"></el-input>
              </el-col>
            </el-row>
          </div>
          <div slot='footer' class='dialog-footer'>
            <el-button type=''  size='small' @click='batch_transfer_show=false'>取消</el-button>
            <el-button type='primary'  size='small' @click='confirmTransfer'>提交</el-button>
          </div>
        </el-dialog>


        <!-- 开启自动售卖 -->
        <el-dialog title='开启自动售卖'  width='400px' :visible.sync='auto_sell_show' class='dialog' >
          <div>
            <div>单笔限额:</div>
            <el-row style="marginTop: 10px;">
              <el-col :span="11">
                <el-input v-model="minmoney" oninput=" value = value.replace(/[^\d.]/g,'').match(/^\d*(\.?\d{0,4})/g)[0] " placeholder="最小额度" size="small"></el-input>
              </el-col>
              <el-col :span="2" style="lineHeight: 30px; textAlign: center;">—</el-col>
              <el-col :span="11">
                <el-input v-model="maxmoney" oninput=" value = value.replace(/[^\d.]/g,'').match(/^\d*(\.?\d{0,4})/g)[0] " placeholder="最大额度" size="small"></el-input>
              </el-col>
            </el-row>
          </div>
          <div slot='footer' class='dialog-footer'>
            <el-button type=''  size='small' @click='auto_sell_show=false'>取消</el-button>
            <el-button type='primary'  size='small' @click='confirmAutoSell'>提交</el-button>
          </div>
        </el-dialog>

    </div>
</template>
<script>
import Query from './components/query.vue'
import Table from './components/table.vue'
import AddPlayer from './components/add.vue'
import SetRate from './components/setRate.vue'

export default {
    data() { 
        return {
            filterForm:{
                page: 1,
                pagesize: 30,
            },
            list:[],
            total:0,
            pageSum:0,//当前页余额汇总
            sum:0,//账户余额汇总
            pageIncome: 0,  // 当前页收益汇总
            incomeSum: 0, // 总收益汇总

            edit_teamname_show: false,
            teamname: '',
            userid: '',

            // 批量转账
            selectList: [],
            batch_transfer_show: false,
            pwd: '',
            form: {
              selectList: [],
            },
            balance: 0,

            // 自动售卖
            auto_sell_show: false,
            minmoney: '',
            maxmoney: '',
        }
    },
    computed: {
      current_transfer_amount() {
        let num = 0
        this.form.selectList.forEach(v => {
          num += Number(v['transfer_amount'])
        })
        return num
      }
    },
    mounted() {
      this.getBlank()
    },
    methods: {
      getBlank() {
        this.$http.post('/channel/index/home', {},).then(res=>{
          this.balance = res.balance.can_use_balance
        })
      },
      getData(filterForm){
            if(filterForm){
                this.filterForm = filterForm
            }
            this.$http.post('/channel/user/getPlayerList',this.filterForm).then((res) => {
                res.data.forEach(v => {
                  v['transfer_amount'] = 0
                  v['randhash'] = this.$tool.randomWord(4,6, true)
                })
                const list = res.data || []
                list.forEach(v => {
                  if (v['dir_user_num'] && v['dir_user_num'] > 0) {
                    v['hasChildren'] = true
                  }
                })
                this.list = list
                this.total = res.total || 0
                this.pageSum = res.pageSum
                this.sum = res.sum
                this.pageIncome = res.pageIncome
                this.incomeSum = res.incomeSum
                
                this.$refs.Table.clearSelection() 
                
            }).catch((res) => {
                console.log(res)
            })
      },
      sortChange(data){
            if(data.order ){
                this.filterForm.sort = data.prop
                this.filterForm.order = (data.order === "descending"?'desc':'asc')
            }else{
                this.filterForm.sort = ''
                this.filterForm.order =  ''
            }
            this.getData()
      }, 
      formatJson(filterVal, jsonData) {
            return jsonData.map(v => filterVal.map(j => v[j]))
      },
      handleCurrentChange(val) {
			  this.filterForm.page = val
			  this.getData()
		  },
		  handleSizeChange(val) {
        this.filterForm.pagesize = val
        this.getData()
      },
      // 修改名称
      showEditTeamname(row) {
        this.edit_teamname_show = true
        this.userid = row.userid
        this.teamname = row.team_name
      },
      editTeamname() {
        if(!this.teamname.trim()){
            this.$message('请输团队名称')
            return
        }
        this.$http.post('/channel/user/modifyTeamname', {
          userID: this.userid,
          newTeamName: this.teamname.trim()
        }).then((res) => {
            this.$message.success('修改团队名称成功')
            this.getData()
            this.edit_teamname_show = false
        }).catch((err) => { 
            console.log(err)
        })
      },
      // 批量转账
      batchTransfer() {
        const data = this.list.filter(v => {
          return this.selectList.includes(v.userid)
        })
        console.log(data)
        data.forEach(v => {
          v['transfer_amount'] = 0
        })
        this.form.selectList = data
        this.pwd = ''
        this.batch_transfer_show = true
      },
      deleteSelect(id) {
        let index = 0
        let is_had = false
        this.form.selectList.forEach((v, n) => {
          if (id === v.userid) {
            is_had = true
            index = n
          }
        })

        if (is_had) {
          console.log(index)
          this.form.selectList.splice(index, 1)
        }
      },
      onInputPercentArr(value, index) {
        console.log(value)
        console.log(index)
        this.form.selectList[index]['transfer_amount'] = value
      },
      confirmTransfer() {
        if(this.pwd.trim() === ''){
            this.$message('请输入资金密码')
            return
        }

        let toUserIDList = []
        let amountList = []
        this.form.selectList.forEach(v => {
          toUserIDList.push(v['userid'])
          amountList.push(v['transfer_amount'])
        })

        if (amountList.includes('')) {
          this.$message('请输入转账金额')
          return
        }

        this.$http.post('/channel/fund/alltransfer', {
          toUserIDList: toUserIDList.join(','),
          amountList: amountList.join(','),
          fundPwd: this.pwd
        }).then((res) => {
            this.$message.success('转账成功')
            this.getData()
            this.getBlank()
            this.batch_transfer_show = false
        }).catch((err) => { 
            console.log(err)
        })
      },
      

      // 是否开启售卖
      doHand(data) {
        if (data.vendornum === 0) {
          // 开启
          this.minmoney = ''
          this.maxmoney = ''
          this.auto_sell_show = true
          this.userid = data.userid
        } else {
          // 关闭
          this.$http.post('/channel/user/changeSelfStatus',{
              status: 0,
              userid:data.userid,
              minmoney: 0,
              maxmoney: 0,
          }).then((res) => {
              this.getData()
          }).catch((err) => { 
            console.log(err)
          })
        }
      },
      confirmAutoSell() {
        if(this.minmoney.trim() === ''){
            this.$message('请输入最小限额')
            return
        }
        if(this.maxmoney.trim() === ''){
            this.$message('请输入最大限额')
            return
        }
        this.$http.post('/channel/user/changeSelfStatus',{
            status: 1,
            userid: this.userid,
            minmoney: this.minmoney.trim(),
            maxmoney: this.maxmoney.trim(),
        }).then((res) => {
            this.getData()
            this.auto_sell_show = false
        }).catch((err) => { 
          console.log(err)
        })
      }
      
    },
    components: {
        Query,
        Table,
        AddPlayer,
        SetRate
    }
}
</script>
<style lang='less' scoped>
.container{
}
</style>