<template>
    <el-dialog title='设置费率'  width='800px' :visible.sync='widgetVisible' :close-on-click-modal=' false ' :center='true' >
        <div  class='dialog'>
            <p>  <span>代理商名称：{{info.nickname}}</span> <span>代理商编号：{{ info.userid }}</span>  </p>
            <div class="tip">
                <h3>收益费率设置:</h3>
                <p>最终收益费率=基础汇率 - 额外费率，如上级0.3%，设置额外费率0.2%，则收益费率为0.1%</p>
            </div>
            <div class="formBox">
                <div> 
                    <el-form inline label-width="130px">
                        <el-form-item :label="`${ payTypeRel[item.paytype]}: `" v-for=" (item,index) in info.optpaylist " :key="index">
                            <el-input :value="item.sell_comm_rate*100" disabled> <span slot="append" >%</span> </el-input> - 
                            <el-input 
                                v-model="info.extrarate[item.paytype].sell_comm_rate" 
                                :class="info.extrarate[item.paytype].sell_comm_rate>item.sell_comm_rate*100&&'red'" 
                                placeholder="0.00" @blur="inputBlur(item)" 
                                oninput=" value = value.replace(/[^\d.]/g,'').match(/^\d*(\.?\d{0,2})/g)[0] "> <span slot="append" >%</span> </el-input>
                        </el-form-item>
                    </el-form>
                </div>
               
            </div>
             
        </div>
        <div slot='footer' class='dialog-footer'>
            <el-button type=''  size='small' @click='widgetVisible=false'>取消</el-button>
            <el-button type='primary'  size='small' @click='setData'>提交</el-button>
        </div>
    </el-dialog>
</template>
<script>
export default { 
    data() { 
        return {
            widgetVisible:false,
            info:{ },
            extrarate:{},
            payTypeRel:{}
        }
    },
    mounted() {
    },
    methods: {
        show(data){  
            this.getData(data.userid)
            this.widgetVisible = true
        },
        inputBlur(data){
            if(this.info.extrarate[data.paytype].sell_comm_rate>data.sell_comm_rate*100){
                this.$message('额外费率不得大于上级费率')
            }
        },
        getData(userid){
            this.$http.post('/channel/user/getPlayerInfo',{userid,userid}).then((res) => {
                this.info = res
                this.payTypeRel = res.payTypeRel
                let extrarate = {}
                this.info.extrarate.forEach(el => { 
                    extrarate[el.paytype] = {
                        buy_extra_fee:el.buy_extra_fee*100,
                        sell_comm_rate:el.sell_comm_rate*100
                    }
                });
                this.$set(this.info,'extrarate',extrarate)
                console.log(this.info)
            }).catch((err) => { 
             console.log(err)
            })
        },
        setData(){ 
            let payTypeList = []
            let incomeRateList =[] 
            this.info.optpaylist.forEach((el)=>{
                let key = el.paytype
                payTypeList.push(key) 
                incomeRateList.push( Math.floor(el.sell_comm_rate*10000- this.info.extrarate[key].sell_comm_rate*100)/10000 ) 
            })
            // console.log(incomeRateList)
            // return 
            this.$http.post('/channel/user/changePlayerRate',{
                currencyUserID:this.info.userid,//：代理商ID
                payTypeList:payTypeList.join(','),//：通道类型列表，  
                incomeRateList:incomeRateList.join(','),//：出售费率列表，  
            }).then((res) => {
                this.$message.success('设置成功')
                this.widgetVisible = false
                this.$parent.getData()
            }).catch((err) => { 
             console.log(err)
            })
        }
    },
    components: {
    }
}
</script>
<style lang='less' scoped>
.dialog{
    &>p{
        display: flex;
        flex-direction: row;
        justify-content: space-between;
        align-items: center;
        box-sizing: border-box;
        padding:  0 ;
        margin-bottom: 20px;
        span{
            flex: 1;
        }
    }
    &>.tip{
        display: flex;
        flex-direction: row;
        justify-content: space-between;
        align-items: center;
        box-sizing: border-box;
        padding:  0 ;
        p{
            flex: 1;

        }
    }
    .formBox{
        display: flex;
        flex-direction: row;
        justify-content: space-between;
        align-items: center;
        box-sizing: border-box;
        padding:  0 ;
    }

}
/deep/ .el-form-item{
    margin-top: 10px;
    .el-form-item__label{
            line-height: 30px;
        }
    .el-form-item__content{
        height: 30px;
        line-height: 30px;
        .el-input{
            width: 90px;
            margin-left: 5px;
            &.red{
                .el-input__inner{
                    border-color: red;
                }
            }
            .el-input__inner{
                height: 30px;
                padding: 0 5px;
                line-height: 30px;
                background: #fff !important;
            }
            &.is-disabled{
                .el-input__inner{
                    background: #EFF2F6 !important;
                }
            }
            
        }
        .el-input-group__append{
            padding: 0 3px;
        }
    }
} 
</style>