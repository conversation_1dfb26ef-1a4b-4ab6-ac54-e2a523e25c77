<template>
    <el-collapse value="filter">
		<el-collapse-item title="查询条件" name="filter">
             <el-form inline size="mini">
                <el-form-item label="关键词:">
                    <el-input v-model="filterForm.keyword" placeholder="编号/昵称"></el-input>
                </el-form-item>
                <el-form-item label="团队名称:">
                    <el-input v-model="filterForm.team_name" placeholder="团队名称"></el-input>
                </el-form-item>
                <el-form-item label="自动售卖状态:">
                    <el-select v-model="filterForm.self_accept_order" placeholder="选择状态">
                        <el-option
                            v-for="item in statusList"
                            :key="item.value"
                            :label="item.label"
                            :value="item.value">
                        </el-option>
                    </el-select>
                </el-form-item>
               
                <el-form-item>
                    <el-button  type="primary" @click.native="getData">查询</el-button>
                    <el-button  type="primary" @click="clear"  >清空</el-button> 
                    <el-button  type="primary" @click.native="addPlayer">创建币商</el-button>
                    <el-button  type="danger" :disabled="selectList.length === 0" @click.native="updateAllPlayerConf(1)">接单开关</el-button>
                    <el-button  type="danger" :disabled="selectList.length === 0" @click.native="updateAllPlayerConf(2)">登录开关</el-button>
                    <el-button  type="danger" :disabled="selectList.length === 0" @click.native="batchTransfer">批量转账</el-button>
                    
                </el-form-item>
            </el-form>
        </el-collapse-item>
    </el-collapse>
</template>
<script>
export default {
    data() { 
        return {
            date:[],
            filterForm:{
                keyword: '',
                team_name: '',
                self_accept_order: '',
                page: 1,
                pagesize: 30,
            },
            statusList:[
                {value:'',label:'全部'},{value:1,label:'已开启'}, {value:2,label:'未开启'}
            ],
            access:{
                adminInExcel: "N",
            },
            selectList:[]
        }
    },
    mounted() {
        this.getData()
    },
    methods: {
        clear(){
            this.filterForm = {
                keyword: '',
                team_name: '',
                self_accept_order: '',
                page: 1,
                pagesize: 30,
            }
            this.$emit('getData',this.filterForm)
        },
        getData(){
            this.filterForm.page = 1 
            this.$emit('getData',this.filterForm)
        },
        addPlayer(){
            this.$parent.$refs.AddPlayer.show()
        },
        updateAllPlayerConf(optID){//optID： 1 是否接单 2 是否允许登陆
            this.$http.post('/channel/user/updateAllPlayerConf',{
                userIdList:this.selectList.join(','), 
                optID:optID,
            }).then((res) => {
                this.getData()
            }).catch((err) => { 
             console.log(err)
            })
        },
        // 批量转账
        batchTransfer() {
          this.$parent.selectList = this.selectList
          this.$parent.batchTransfer()
        }
       
    },
    watch: {
    },

    components: {
    }
}
</script>
<style lang='less' scoped>
.query{
}
</style>