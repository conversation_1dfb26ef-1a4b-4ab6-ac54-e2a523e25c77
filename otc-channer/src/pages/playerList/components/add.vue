<template>
 <el-dialog title='创建币商'  width='400px' :visible.sync='widgetVisible' :close-on-click-modal=' false ' class='dialog' :center='true' >
    <div>
        <el-form inline label-width="100px">
            <el-form-item label="币商名称:" >
                <el-input v-model="filterForm.userName" placeholder="币商名称"></el-input>
            </el-form-item>
            <el-form-item label="团队名称:" >
                <el-input v-model="filterForm.teamname" placeholder="团队名称"></el-input>
            </el-form-item>
             <el-form-item label="账号:" >
                <el-input v-model="filterForm.account" oninput=" value = value.replace(/[^\d]/g,'') " placeholder="手机号"></el-input>
            </el-form-item>
             <el-form-item label="登陆密码:">
                <el-input v-model="filterForm.pwd" type="password" placeholder="登陆密码"></el-input>
            </el-form-item>
            <el-form-item label="类型:">
               <!--  <el-radio v-model="filterForm.userType" label="201">码商</el-radio> -->
                <el-radio v-model="filterForm.userType" label="202">币商</el-radio>
            </el-form-item> 
        </el-form>
    </div>
  <div slot='footer' class='dialog-footer'>
   <el-button type=''  size='small' @click='widgetVisible=false'>取消</el-button>
   <el-button type='primary'  size='small' @click='addPlayer'>提交</el-button>
  </div>
 </el-dialog>
</template>
<script>
export default {
 data() { 
  return {
    widgetVisible:false,
    filterForm:{ 
        userName:'',//：币商名称
        account:'',//：账号
        pwd:'',//：密码 
        teamname: '', // 团队名称
    }
  }
 },
 mounted() {
 },
 methods: {
    show(){ 
        this.filterForm = { 
                userName:'',//：币商名称
                account:'',//：账号
                pwd:'',//：密码 
                userType:'',
                teamname: '',
            }
        this.widgetVisible = true
    }, 
    addPlayer(){
        if(!this.filterForm.userName ){
            this.$message('请输入币商名称')
            return
        }
        if(!this.filterForm.teamname ){
            this.$message('请输入团队名称')
            return
        }
        if(!this.filterForm.account ){
            this.$message('请输入币商账号')
            return
        }
        if(!this.filterForm.pwd ){
            this.$message('请输入密码')
            return
        }
        if(!this.filterForm.userType ){
            this.$message('请选择类型')
            return
        }
        this.$http.post('/channel/user/addPlayer',this.filterForm).then((res) => {
            this.$message.success('创建成功')
            this.$parent.getData()
            this.widgetVisible = false
        }).catch((err) => { 
            console.log(err)
        })
    }
 },
 components: {
 }
}
</script>
<style lang='less' scoped>
.dialog{
    
}
/deep/.el-form-item{
        margin-bottom: 10px !important;
        &:last-of-type{
            margin-bottom: 0 !important;
        }
    }
</style>