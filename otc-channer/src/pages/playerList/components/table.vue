<template>
    <el-table
      :data="list"
      border
      size="mini"
      height="auto"
      stripe
      :row-key="fun_userid"
      lazy
      :load="load"
      :tree-props="{children: 'children', hasChildren: 'hasChildren'}"
      ref="multipleTable"
      @selection-change="handleSelectionChange"
    >
        <el-table-column type="selection" width="40"> </el-table-column>
        <!-- <el-table-column type="index" width="50" align="center"></el-table-column> -->
        <el-table-column prop="userid" label="用户编号"  width="110" align="center"></el-table-column>
        <!-- <el-table-column prop="agent" label="上级id"  width="80" align="center"></el-table-column> -->
        <el-table-column prop="nickname" label="昵称" align="center" width="100px" show-overflow-tooltip></el-table-column>
        <el-table-column prop="team_name" label="团队名称" align="center" width="100px"></el-table-column>
        <el-table-column prop="user_type" label="类型"   align="center">
            <div slot-scope="scope" >
                {{  scope.row.user_type==201?'码商':'币商'}}  
            </div>
        </el-table-column>
        
        <el-table-column prop="money" label="账户余额" width="250" align="center">
            <div slot-scope="scope" >
                {{ $tool.BigJs(scope.row.money,1,3,4) }} USDT
            </div>
        </el-table-column>
        <el-table-column label="团队余额" align="center" width="200px">
          <div slot-scope="scope" >
            {{ $tool.BigJs(scope.row.team_balance_amount,1,3,4) }} USDT
          </div>
        </el-table-column>
        <el-table-column label="交易额" width="250" align="center">
            <div slot-scope="scope" >
                <div>充值：{{ $tool.BigJs(scope.row.team_recharge_amount,1,3,4) }} USDT</div>
                <div>提现：{{ $tool.BigJs(scope.row.team_withdraw_amount,1,3,4) }} USDT</div>
            </div>
        </el-table-column>
        <!-- <el-table-column prop="day_amount" label="日交易额" width="250" align="center">
            <div slot-scope="scope" >
                {{ $tool.BigJs(scope.row.day_amount,1,3,4) }} USDT
            </div>
        </el-table-column> -->
        <el-table-column align="center" label="费率" width="300" show-overflow-tooltip>
            <div slot-scope="scope">
                <span  v-for=" (item,index) in scope.row.pay_list " :key="index">
                    {{item.paytype_name}}:{{item.sell_comm_rate*100}}%;
                </span>
            </div>
        </el-table-column>
        <el-table-column label="收益" align="center" width="200px">
          <div slot-scope="scope" >
            {{ $tool.BigJs(scope.row.team_income_amount,1,3,4) }}
          </div>
        </el-table-column>
        <el-table-column align="center" label="是否接单" width="80">
            <div slot-scope="scope"> 
                <el-switch v-if="!scope.row.is_next" :value=" scope.row.is_team_accept_order === 0 " @change="switchChange(1,scope.row)" active-color="#13ce66" inactive-color="#ff4949"> </el-switch>
                    <!-- 0 允许1 禁止 -->
            </div>
        </el-table-column>
        <el-table-column align="center" label="开启售卖" width="100">
            <div slot-scope="scope"> 
                <el-switch :value=" scope.row.vendornum > 0 " @change="switchChange(4,scope.row)" active-color="#13ce66" inactive-color="#ff4949"> </el-switch>
                    <!-- 售卖状态，0=关闭，大于0=开启 -->
            </div>
        </el-table-column>
        <el-table-column align="center" label="允许登录APP" width="100">
            <div slot-scope="scope"> 
                <el-switch :value=" scope.row.prohibit_login === 0 " @change="switchChange(2,scope.row)" active-color="#13ce66" inactive-color="#ff4949"> </el-switch>
                    <!-- 是否锁定 0解锁(允许) 1锁定(禁止) -->
            </div>
        </el-table-column>
       <!--  <el-table-column align="center" label="允许登录渠道后台" width="120">
            <div slot-scope="scope"> 
                <el-switch :value=" scope.row.is_allow_loginback === 0 " @change="switchChange(3,scope.row)" active-color="#13ce66" inactive-color="#ff4949"> </el-switch>
                   
            </div>
        </el-table-column>  -->
        <el-table-column prop="regdate" label="创建时间"  width="150" align="center"></el-table-column>
        <el-table-column label="操作" width="80" align="center" fixed="right">
            <div slot-scope="scope" v-if="scope.row.op">
            <el-button
                v-for=" (item,index) in scope.row.op "
                :key="index"
                type="text"
                @click="btnClick(scope.row,item)"
            >{{ item.btn_name }}</el-button>
            </div>
            <div v-else  >--</div>
        </el-table-column> 
    </el-table>    
</template>
<script>
export default {
  props: {
    list: {
      type: Array,
      default: [],
    },
    tableLoading: {
      type: Boolean,
      default: false,
    },
  },
  data() {
    return {
        selectList:''
    };
  },
  mounted() {},
  methods: {
    fun_userid(row){
        return `${row.userid}-${row.randhash}`
    },
    btnClick(row, item) {
      // 设置费率
      if (item.btn_name === '设置费率') {
        this.$parent.$refs.SetRate.show(row)
      }
      // 修改团队名称
      if (item.btn_name === '修改') {
        this.$parent.showEditTeamname(row)
      }
    }, 
    switchChange(type,data){//1:是否接单,2:允许登录APP,3:允许登录渠道后台,
            switch (type) {
                case 1:
                    let queryData = {
                        userID:data.userid,
                        status:data.is_team_accept_order === 0?1:0
                    }
                    this.updateTeamLineConf(queryData)
                    break;
                case 2:
                    
                    queryData = {
                        currencyUserID:data.userid,
                        optID:2,
                        optType:data.prohibit_login === 0?1:0
                    }
                    this.updatePlayerConf(queryData)
                    break;
                case 3:
                    this.isAllowLogin(data)
                    break;
                case 4:
                    this.isAllowVendornum(data)
                    break;
            
                default:
                    break;
            }
    },
    async load(tree, treeNode, resolve) {
        // this.$parent.loadChild(tree.userid)
        const query = {
          agentid: tree.userid
        }
        const res = await this.$http.post('/channel/user/getNextUser', query)
        if (res) {
          res.data.forEach(v => {
            v['is_next'] = true
            v['transfer_amount'] = 0
            v['randhash'] = this.$tool.randomWord(4,6, true)
          })
          const list = res.data || []
          list.forEach(v => {
            if (v['dir_user_num'] && v['dir_user_num'] > 0) {
              v['hasChildren'] = true
            }
          })
          
          this.$refs.multipleTable.clearSelection() 
          resolve(list)
        }
    },
    clearSelection(){
        this.$refs.multipleTable.clearSelection();
    },
    updatePlayerConf(data){
        this.$http.post('/channel/user/updatePlayerConf',data).then((res) => {
            this.$parent.getData()
        }).catch((err) => { 
            console.log(err)
        })
    },
    updateTeamLineConf(data){
        this.$http.post('/channel/user/setTeamname',data).then((res) => {
            this.$parent.getData()
        }).catch((err) => { 
            console.log(err)
        })
    },
    isAllowLogin(data){
        this.$http.post('/channel/user/isAllowLogin',{
            is_allow_loginback: data.is_allow_loginback === 0?1:0 ,
            userid:data.userid
        }).then((res) => {
            this.$parent.getData()
        }).catch((err) => { 
         console.log(err)
        })
    },
    isAllowVendornum(data) {
      this.$emit('do-hand', data)
    },
    handleSelectionChange(val) {
        let selectList = [];
        // let selectListRow = []
        val.forEach(element => {
            selectList.push(element.userid)
            // element['transfer_amount'] = 0
            // selectListRow.push(element)
        });
        // console.log(selectList)
        this.$parent.$refs.Query.selectList = selectList
        // this.$parent.selectList = selectListRow
    }
  },
  components: {},
};
</script>
<style lang='less' scoped>
.table {
}
</style>