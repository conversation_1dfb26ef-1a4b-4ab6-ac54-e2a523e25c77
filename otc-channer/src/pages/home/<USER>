<template>
  <div>
    <div class="searchHeader">
      <el-button type="primary" icon="el-icon-notebook-2" size="small" @click="add">新增菜单</el-button>
    </div>
    <div class="tableList">
      <el-table
        :data="tableData" border
        :height="tableHeight" style="width: 100%" :cell-style="rowClass"
        :row-style="{'font-size':'12px','color':'#324253'}"
        :header-row-style="{'font-size':'12px','color':'#1f2021'}">

        <!-- <el-table-column align="center" prop="pid" label="主菜单" width="">
           <template slot-scope="scope">
            <span style="color:red" >{{scope.row.group_label}}</span>
          </template>
        </el-table-column> -->
        <el-table-column align="center" prop="pid" label="菜单" width="">
           <template slot-scope="scope">
            <span v-if="scope.row.pid===0">{{scope.row.title}}</span>
          </template>
        </el-table-column>
        <!-- <el-table-column align="center" prop="title" label="标识" width="">
          <template slot-scope="scope">
            <span v-if="scope.row.pid!==0">{{scope.row.title}}</span>
          </template>
        </el-table-column> -->
        <el-table-column align="center" prop="describe" label="描述" width=""></el-table-column>
        <el-table-column align="center" prop="sort" label="排序" width=""></el-table-column>
        <el-table-column align="center" prop="status" label="状态" width="">
          <template slot-scope="scope">
            <span style="color:red" v-if="scope.row.status == 0">关闭</span>
            <span v-else-if="scope.row.status == 1">开启</span>
          </template>
        </el-table-column>
        <el-table-column align="center" prop="name" label="权限URL" width=""></el-table-column>
        <el-table-column align="center" label="操作" width="">
          <template slot-scope="scope">
            <span style="color:#236DF4;cursor:pointer" @click="edit(scope.row)">编辑</span>
            <!-- <span style="color:red;cursor:pointer;margin-left:10px;" @click="dele(scope.row)">删除</span> -->
          </template>
        </el-table-column>
      </el-table>
      <!-- <pagination
        v-show="total>0"
        @handleChange="handleCurrentChange"
        :total="+total"
        :page-size="option.pagesize"
        :curPage="option.page"
      ></pagination> -->
    </div>



    <el-dialog title="权限管理"
      width="500px" :visible.sync="dialogFormVisible"
      :close-on-click-modal="false">
      <el-form :model="formData">
        <!-- <el-form-item label="主菜单:" label-width="100px">
          <el-select v-model="formData.group" style="width:100%" size="small" placeholder="顶层菜单">
            <el-option :label="item.group_label" :value="item.group" v-for="(item,index) in topMenus" :key="index"></el-option>
          </el-select>
        </el-form-item> -->
        <!-- <el-form-item label="菜单:" label-width="100px">
          <el-select v-model="formData.pid"
            style="width:100%" size="small" placeholder="请输入"
            filterable allow-create default-first-option >
            <el-option :label="item.title" :value="item.id" v-for="(item,index) in secondMenuList" :key="index"></el-option>
          </el-select>
        </el-form-item> -->
        <el-form-item label="菜单名称:" label-width="100px">
          <el-input size="small" style="width:100%" v-model="formData.title" placeholder="菜单名称"></el-input>
        </el-form-item>
        <el-form-item label="菜单描述:" label-width="100px">
          <el-input size="small" style="width:100%" v-model="formData.describe" placeholder="菜单描述"></el-input>
        </el-form-item>
        <el-form-item label="排序:" label-width="100px">
          <el-input size="small" style="width:100%" v-model="formData.sort" placeholder="排序"></el-input>
        </el-form-item>
        <el-form-item label="状态:" label-width="100px">
          <el-select v-model="formData.status"
            style="width:100%" size="small" placeholder="请选择状态">
            <el-option label="开启" :value="1"></el-option>
            <el-option label="关闭" :value="0"></el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="菜单地址:" label-width="100px">
          <el-input size="small" style="width:100%" v-model="formData.name" placeholder="菜单URL"></el-input>
        </el-form-item>
        <el-form-item label="图标名称:" label-width="100px">
          <el-input size="small" style="width:100%" v-model="formData.ico" placeholder="图标名称"></el-input>
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button type="primary" @click="addSure">确 定</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
// import md5 from "js-md5";
// import _ from 'lodash';
export default {
  name: "menuMgr",
  data: function() {
    return {
      tableData: [],
      total: 0,
      option: {
        
      },
      formData: {
        status: 1,
      },
      tableHeight: 500,
      dialogFormVisible: false,
      addOrEdit: "add",
      secondMenuList: [],
    };
  },
  methods: {
    getList() {
      this.tableData = [];
      this.$http.get(this.$tool.api.menus, this.option).then(res => {
        this.tableData = res;
        // this.secondMenuList = [{'title':'一级菜单','id':0}];
        this.tableData = res;
      });
    },
    rowClass({row, column, rowIndex, columnIndex}){
        // if(row.group && row.pid==null){
        //     return  'background:#454545;font-size:14px;font-weight:500'
        // }else if(row.group && row.pid==0){
        //     return  'background:#e5e5e5;font-size:12px;'
        // }
    },

    edit({ id, sort, name, title, ico, status, describe }) {
      this.addOrEdit = "edit";
      this.formData = { id, sort, name, title, ico, status, describe };
      this.dialogFormVisible = true;
    },
    add() {
      this.addOrEdit = "add";
       this.formData = {};
      this.dialogFormVisible = true;
    },
    addSure() {
      if (!this.formData.title) {
        this.$notify({
          title: "提示",
          message: `名称，排序不能为空`,
          type: "error"
        });
        return;
      }
      if (this.addOrEdit == "add") {
        this.$http.get(this.$tool.api.addMenu, { ...this.formData, group_label:'', group: '', pid: 0}).then(res => {
          this.$notify({
            title: "提示",
            message: `新增成功，请在后台账号管理相应位置设置相关人员权限！`,
            type: "success"
          });
          this.dialogFormVisible = false;
          // this.getMenuList();
          this.getList();
        });
      } else {
        this.$http.get(this.$tool.api.setMenu, { ...this.formData, group_label:'', group: '', pid: 0}).then(res => {
          this.$notify({
            title: "提示",
            message: `修改成功`,
            type: "success"
          });
          this.dialogFormVisible = false;
          // this.getMenuList();
          this.getList();
        });
      }
    },
    getMenuList(){
      // this.$http.get(this.$tool.api.selectMenuList,{uid:localStorage['xpay_userId']}).then(res=>{
          // let menuList = JSON.parse(res).menu;
          // this.$store.dispatch('getMenuList',menuList);
      // })
    },
    // dele({menuCode}){
    //   this.$confirm("您确定删除吗?", "提示", {
    //     confirmButtonText: "确定",
    //     cancelButtonText: "取消",
    //     type: "warning"
    //   }).then(() => {
    //     this.$http
    //       .get(this.$tool.api.deleteMenu, { menuCode })
    //       .then(res => {
    //         this.$notify({
    //           title: "提示",
    //           message: `删除成功`,
    //           type: "success"
    //         });
    //         this.getList();
    //       });
    //   });
    // }
  },
  mounted() {
    this.getList();
  }
};
</script>

<style scoped>
span.red {
  color: red;
}
span.lose {
  color: rgb(0, 159, 199);
}
span.ping {
  color: #22ac38;
}
</style>