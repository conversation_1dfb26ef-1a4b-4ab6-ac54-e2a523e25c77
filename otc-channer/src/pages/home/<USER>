<template>
  <div class="homepage">
    <div class="head">
      <h3>尊敬的用户: {{userInfo.nickname}}</h3>
      <div>欢迎您！上次登录时间：{{userInfo.lasttime }}， 登录IP: {{userInfo.ip}}</div>
    </div> 
    <div class="todolist">
        <div class="item">
            <div>代理价格</div>  
            <div>汇率：{{ $tool.BigJs(changeRate.sell_price,1,3,2) }}</div>
        </div>
        <div class="item">
            <div>可用余额</div>  
            <div>{{ $tool.BigJs(balance.can_use_balance,1,3,4) }} USDT</div>
        </div>
        <div class="item">
            <div>今日收益</div>
            <div>{{ $tool.BigJs(balance.today_recharge_amount,1,3,4) }} USDT </div>
        </div>
        <div class="item">
            <div>昨日收益</div>
            <div>{{ $tool.BigJs(balance.yestoday_recharge_amount,1,3,4) }} USDT </div>
        </div>
    </div>
    <div class="toolbox">
      <el-row>
        <el-col :span="5">
          <el-card class="box-card">
            <el-tag type="success" effect="dark">今日出售</el-tag>
            <div class="box-card-item">
              <div>
                <div>订单金额：</div>
                <div>
                  <p style="color: #f00;">￥{{todaySellData.orderRMBAmount | Tslice}}</p>
                  <p>{{todaySellData.orderUSDTAmount | Fslice}} USDT</p>
                </div>
              </div>
              <div>
                <span>订单总数：</span>
                <span>{{todaySellData.orderAllNum}}笔</span>
              </div>
              <div>
                <span>已完成数：</span>
                <span>{{todaySellData.orderFinfishNum}}笔</span>
              </div>
              <div>
                <span style="letter-spacing: 4px;">成功率：</span>
                <span>{{todaySellData.orderSuccessRate}}</span>
              </div>
              <div>
                <div>出售收益：</div>
                <div>
                  <p>{{todaySellData.orderCommission | Fslice}} USDT</p>
                  <p style="color: #f00;">≈￥{{todaySellData.orderRMBCommission | Tslice}}</p>
                </div>
              </div>
            </div>
          </el-card>
        </el-col>
        <el-col :span="5">
          <el-card class="box-card">
            <el-tag type="success" effect="dark">今日购买</el-tag>
            <div class="box-card-item">
              <div>
                <div>订单金额：</div>
                <div>
                  <p style="color: #f00;">￥{{todayBuyData.orderRMBAmount | Tslice}}</p>
                  <p>{{todayBuyData.orderUSDTAmount | Fslice}} USDT</p>
                </div>
              </div>
              <div>
                <span>订单总数：</span>
                <span>{{todayBuyData.orderAllNum}}笔</span>
              </div>
              <div>
                <span>已完成数：</span>
                <span>{{todayBuyData.orderFinfishNum}}笔</span>
              </div>
              <div>
                <span style="letter-spacing: 4px;">成功率：</span>
                <span>{{todayBuyData.orderSuccessRate}}</span>
              </div>
              <div>
                <div>购买收益：</div>
                <div>
                  <p>{{todayBuyData.orderCommission | Fslice}} USDT</p>
                  <p style="color: #f00;">≈￥{{todayBuyData.orderRMBCommission | Tslice}}</p>
                </div>
              </div>
            </div>
          </el-card>
        </el-col>
        <el-col :span="5">
          <el-card class="box-card">
            <el-tag type="warning" effect="dark">昨日出售</el-tag>
            <div class="box-card-item">
              <div>
                <div>订单金额：</div>
                <div>
                  <p style="color: #f00;">￥{{yestodaySellData.orderRMBAmount | Tslice}}</p>
                  <p>{{yestodaySellData.orderUSDTAmount | Fslice}} USDT</p>
                </div>
              </div>
              <div>
                <span>订单总数：</span>
                <span>{{yestodaySellData.orderAllNum}}笔</span>
              </div>
              <div>
                <span>已完成数：</span>
                <span>{{yestodaySellData.orderFinfishNum}}笔</span>
              </div>
              <div>
                <span style="letter-spacing: 4px;">成功率：</span>
                <span>{{yestodaySellData.orderSuccessRate}}</span>
              </div>
              <div>
                <div>出售收益：</div>
                <div>
                  <p>{{yestodaySellData.orderCommission | Fslice}} USDT</p>
                  <p style="color: #f00;">≈￥{{yestodaySellData.orderRMBCommission | Tslice}}</p>
                </div>
              </div>
            </div>
          </el-card>
        </el-col>
        <el-col :span="5">
          <el-card class="box-card">
            <el-tag type="warning" effect="dark">昨日购买</el-tag>
            <div class="box-card-item">
              <div>
                <div>订单金额：</div>
                <div>
                  <p style="color: #f00;">￥{{yestodayBuyData.orderRMBAmount | Tslice}}</p>
                  <p>{{yestodayBuyData.orderUSDTAmount | Fslice}} USDT</p>
                </div>
              </div>
              <div>
                <span>订单总数：</span>
                <span>{{yestodayBuyData.orderAllNum}}笔</span>
              </div>
              <div>
                <span>已完成数：</span>
                <span>{{yestodayBuyData.orderFinfishNum}}笔</span>
              </div>
              <div>
                <span style="letter-spacing: 4px;">成功率：</span>
                <span>{{yestodayBuyData.orderSuccessRate}}</span>
              </div>
              <div>
                <div>购买收益：</div>
                <div>
                  <p>{{yestodayBuyData.orderCommission | Fslice}} USDT</p>
                  <p style="color: #f00;">≈￥{{yestodayBuyData.orderRMBCommission | Tslice}}</p>
                </div>
              </div>
            </div>
          </el-card>
        </el-col>
      </el-row>
    </div>
  </div>
</template>


<script>
import { mapMutations } from 'vuex'
export default {
  name: "home",
  data() {
    return {
      userInfo: {},
      balance: {},
      changeRate: {},
      
      // data
      todayBuyData: {},
      todaySellData: {},
      yestodayBuyData: {},
      yestodaySellData: {},
    };
  },
  computed:{
   
  },
  created(){
    // this.userInfo = JSON.parse(localStorage.getItem('xpay_userInfo'))  
  },
  mounted(){
    this.getData()
  },
  methods: {
    getData(){
      this.$http.post('/channel/index/home', {},).then(res=>{
        this.userInfo = res.userInfo
        this.balance = res.balance
        this.changeRate = res.changeRate
        
        const { todayBuyData, todaySellData, yestodayBuyData, yestodaySellData } = res.payData
        this.todayBuyData = todayBuyData
        this.todaySellData = todaySellData
        this.yestodayBuyData = yestodayBuyData
        this.yestodaySellData = yestodaySellData

      })
    }
  }, 
};
</script>

<style lang="less" scoped>
 @media screen and (max-width: 1250px){
  .rate{ margin-bottom: 10px }
}
.homepage{
  padding: 10px;
  display: block;
  .head{
    background: #dfebee;
    padding: 18px 25px 0; margin-bottom: 30px;
    div{
      height: 48px; line-height: 48px;
    }
  }
  >h2{
    padding-left: 30px; position: relative;
    margin: 15px auto; font-size: 18px;
    &::after{
      content: '';
      display: block;
      position: absolute;
      width: 4px;
      height: 70%;
      background: #58e988;
      top: 15%;
      left: 18px;
    }
  }
  .todolist{
    display: flex;
    align-items: center;
    justify-content: flex-start;
    padding: 10px 30px; margin-bottom: 40px;
    .item{ 
        flex: 1;
        padding: 40px 15px;
        text-align: center;
        display: flex;
        align-items: center;
        justify-content: space-evenly;
        flex-flow: column;
        border: 1px solid #dddddd;
        border-radius: 6px;margin-right: 14px;
        font-weight: 600;
        font-size: 16px;
        >div:first-child{
            font-size: 14px;
            color: #fff;
            background: #4390ff;
            padding:6px 20px ;
            border-radius: 5px;
            margin-bottom: 10px;
        }
    }
  }
  .dataView{
    display: flex;
    align-items: center;
    flex-wrap: wrap;
    justify-content: flex-start;
    padding: 10px 30px;
    .rate{
      height: 152px;
      width: 280px;
      border: 3px solid #e0e0e0;
      background: #ffffff;
      margin-right: 45px; border-radius: 6px;
      padding: 15px ;  
      >div:first-child{
        color: #000; font-size: 16px; font-weight: 600;
        margin-bottom: 18px;
      }
      >div{
        margin-bottom: 12px;
      }
     
    }
    
    .data{
      >div{
        display: flex; align-items: center;
        justify-content: flex-start;
        margin-bottom: 15px; flex-wrap: wrap;
        &:last-child{
          margin-bottom: 0;
        }
      }
      .item{
        border: 3px solid #e0e0e0;
        background: #ffffff; width: 190px;
        margin-right: 15px; 
        padding: 8px 0px; border-radius: 6px;
        >div{
          height: 24px; line-height: 24px;
          text-align: center;
        }
      }
    }
  }

  .toolbox {
    >div {
      display: flex;
      justify-content: space-between;
      .box-card-item {
        padding: 20px 0 0; 
        >div {
          margin-bottom: 5px;
          display: flex;
        }
      }
    }
  }
}
</style>
