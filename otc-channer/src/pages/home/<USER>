<template>
  <div class="sys-box">
    <el-container>
      <el-header style="min-width:1150px">
        <div class="header-box">
          <div class="left-header">
            <!-- <img src="../../assets/image/logo.png" alt /> -->
            <span>渠道管理后台</span>
          </div>
          <ul class="stati">
            <li class="usrName_logout" style="color:#FF3828;padding-right:0" @click="goLogout">
              <i></i>
              退出
            </li>
            <li class="usrName_logout">你好，{{account}}</li>
            <!-- <li class="usrName_logout" @click="changePwd">修改密码</li> -->
          </ul>
        </div>
      </el-header>

      <el-container>
        <el-aside class="sac-aside" :style="{width:`${isCollapse?'auto':'230px'}`}">
          <div class="menu">
            <el-menu
              class="el-menu-vertical-demo"
              router
              background-color="#0a1a31"
              text-color="#f8f8f8"
              active-text-color="#409eff"
              unique-opened
              :default-active="$route.path"
              size="mini"
              :collapse="isCollapse"
            >
              <template v-for="(parentItem,index) in finnMenuLists">
                <el-submenu v-if="parentItem.childMenus " :key="index" :index="`${index+1}`">
                  <template slot="title">
                    <i
                      :class="['common-icon',`${parentItem.ico}`]"
                      style="transform: scale(1.3);color: #fff;"
                    ></i>
                    <span>{{parentItem.title }}</span>
                  </template>
                  <template >
                    <el-menu-item
                      :key="childIndex"
                      v-for="(item,childIndex) in parentItem.childMenus"
                      :index="`${item.path}`"
                      :route="item.path"
                    >{{(item.title)}}</el-menu-item>
                  </template>
                </el-submenu>

                <el-menu-item
                  v-else
                  :key="index"
                  :route="parentItem.path"
                  :index="`${parentItem.path}`"
                >
                  <i
                    :class="['common-icon',`${parentItem.ico}`]"
                    style="transform: scale(1.3);color: #fff;"
                  ></i>
                  <span slot="title">{{parentItem.title}}</span>
                  <!-- {{parentItem.title}} -->
                </el-menu-item>
              </template>
            </el-menu>
          </div>
        </el-aside>
        <el-main>
          <transition name="el-zoom-in-center">
            <!-- <keep-alive :exclude="/NotAlive$/"> -->
              <router-view class="router-view"></router-view>
            <!-- </keep-alive> -->
          </transition>
        </el-main>
      </el-container>
    </el-container>

    <el-dialog
      title="修改密码"
      width="500px"
      :visible.sync="DialogFormVisible"
      :close-on-click-modal="false"
      @close="closeDialog"
        >
      <el-form :model="form">
        <el-form-item label="原密码:" label-width="130px">
          <el-input
            size="small"
            autocomplete="off"
            type="password"
            style="width:100%"
            v-model="form.oldLoginPwd"
            placeholder="原密码"
          ></el-input>
        </el-form-item>
        <el-form-item label="新密码:" label-width="130px">
          <el-input
            size="small"
            autocomplete="off"
            type="password"
            style="width:100%"
            v-model="form.newPassword"
            placeholder="新密码"
          ></el-input>
        </el-form-item>
        <el-form-item label="确认密码:" label-width="130px">
          <el-input
            size="small"
            autocomplete="off"
            type="password"
            style="width:100%"
            v-model="form.newLoginPwd"
            placeholder="确认密码"
          ></el-input>
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button type="primary" @click="editPwd">确 定</el-button>
      </div>
    </el-dialog>
  </div>
</template>


<script>
import { mapMutations } from "vuex";
export default {
  name: "index",
  data() {
    return {
      account: "",
      isCollapse: false,
      hasDifBackground: false,
      DialogFormVisible: false,
      form: {},
      finnMenuLists: [],
      isAdmin:''
    };
  },
  computed: {},
  methods: {
    ...mapMutations(["userMenus", "SetUserSet"]),
    changeCollapse() {
      if (this.isCollapse == true) {
        this.isCollapse = false;
      } else {
        this.isCollapse = true;
      }
    },
    goLogout() {
      this.$confirm("是否退出登录", "提示", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning",
      })
        .then(() => {
          this.logout();
        })
        .catch(() => {});
    },
    logout() {
      this.$http.get('/channel/login/logout', {}).then((res) => {
        localStorage.removeItem("xpay_userInfo");
        
        this.$router.replace({ name: "login" });
      });
    },
    changePwd() {
      this.DialogFormVisible = true;
    },
    editPwd() {
      if (
        !this.form.oldLoginPwd ||
        !this.form.newPassword ||
        !this.form.newLoginPwd
      ) {
        this.$notify({
          title: "提示",
          message: `不能为空`,
          type: "error",
        });
        return;
      }
      if (this.form.newPassword != this.form.newLoginPwd) {
        this.$notify({
          title: "提示",
          message: `两次密码不一致`,
          type: "error",
        });
        return;
      }
      this.$http
        .get(this.$tool.api.change_pass, {
          uid: localStorage["xpay_userId"],
          oldpassword: this.form.oldLoginPwd,
          password: this.form.newPassword,
          repassword: this.form.newLoginPwd,
        })
        .then(() => {
          this.$notify({
            title: "提示",
            message: `修改成功`,
            type: "success",
          });
          this.DialogFormVisible = false;
        });
    },
    closeDialog() {
      this.form = {};
    },
    getUserSet() {
      this.$http.get('/channel/user/userInfo', {}).then((res) => {
        this.SetUserSet(res);
      });
    },
  },
  mounted() {
    this.getUserSet(); 
    this.account = JSON.parse(localStorage.getItem("xpay_userInfo")).username;
    this.isAdmin = JSON.parse(localStorage.getItem("xpay_userInfo")).isadmin||'' 
    this.finnMenuLists = [
        { path: "/home", title: "首页", ico: "el-icon-s-home " },
    //   
        {
            ico: "el-icon-user",
            title: "下级管理", 
            childMenus: [
                { path: "/playerList", title: "币商管理",}, 
                { path: "/collectionRecord", title: "收款账号",}, 
                /* { path: "/agentRelation", title: "代理关系",},  */
            
            ],
        },
        { path: "/payOrderList", title: "交易记录", ico: "el-icon-document " },
        {
            ico: "el-icon-s-order",
            title: "资金管理", 
            childMenus: [
            { path: "/myasset", title: "我的资产",},
            { path: "/liftCoin", title: "提币记录", },
            { path: "/Earnings", title: "收益明细"},
           /*  { path: "/transferRecord", title: "转账记录"}, */
            
            ],
        },
        { path: "/accountInfo", title: "账户与安全", ico: "el-icon-s-check"}, 
      
     
    ];
    /* if(JSON.parse(localStorage.getItem("xpay_userInfo")).fund_open===111){ //fund_open：111=允许资金调度 100=禁止资金调度
        this.finnMenuLists[1].childMenus.push({ path: "/balanceMove", title: "资金调度",})
    } */
  },
};
</script>
<style lang="less" scoped>
span {
  font-size: 16px;
}
li {
  font-size: 16px;
}
.sac-aside {
  height: 100%;
  overflow-y: auto; 
  .el-menu-vertical-demo:not(.el-menu--collapse) {
    height: calc(100vh - 60px);
    border: none;
    background: #0a1a31!important;
  }
  .common-icon {
    background-size: 100% !important;
    display: inline-block;
    margin: 0 5px;
    vertical-align: text-top;
  }
}
.el-main {
  height: 100%;
  background: #eceff4;
  padding: 0px;
  overflow: hidden;
  > div {
    background: #ffffff;
    height: 100%;
    width: 100%;
    padding: 10px;
    overflow-y: auto;
    &.welcome {
      overflow-y: hidden;
    }
  }
}
.sys-box {
  // overflow-x: scroll;
}
.el-header {
  background: #4390ff;
  color: #fff;
  .header-box {
    height: 100%;
    display: flex;
    align-items: center;
    justify-content: space-between;
    .back_icon {
      display: inline-block;
      width: 7px;
      height: 7px;
      background: #324253;
      border-radius: 50%;
      float: left;
      margin-top: 27px;
    }
    .left-header {
      display: flex;
      align-items: center;
      img {
        height: 40px;
      }
      span {
        font-size: 24px;
        display: inline-block;
        margin-left: 15px;
      }
    }
    .stati {
      height: 100%;
      overflow: hidden;
      li {
        float: left;
        margin-left: 20px;
        height: 100%;
        line-height: 60px;
        font-size: 16px;
        &:nth-child(1) {
          margin-left: 10px;
        }
        &.usrName_logout {
          float: right;
          font-size: 14px;
          padding: 0 18px;
          margin-left: 0;
          font-weight: 600;
          cursor: pointer;
          i {
            display: inline-block;
            width: 14px;
            height: 14px;
            vertical-align: middle;
            //background: url("../../../assets/logout.png") no-repeat center / 100% 100%;
          }
        }
      }
    }
  }
}
.el-container {
  background-color: #061b39;
   background: #0a1a31!important;
}
/deep/.sac-aside { 
    .el-menu-item,.el-submenu__title{ 
        font-size: 14px;
        span{
            font-size: 14px;
        }
       &:hover, &:focus{
            background-color: #263751!important;
        }
    }
    .is-opened{
        position: relative;
        &:before {
            content: "";
            position: absolute;
            left: 0;
            top: 0;
            width: 3px;
            height: 100%;
            transition: background .3s;
            background-color: #4390ff;
            z-index: 1;
        }
        .el-menu--inline{
            background-color: #081f43!important
        }
    }
}
</style>
