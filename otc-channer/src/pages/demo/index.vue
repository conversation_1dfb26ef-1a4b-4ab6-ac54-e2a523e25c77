<template>
    <div class='container'>
        <Query ref="Query" @getData = " getData " @exportExcel = " exportExcel "></Query>
        <Table ref="Table" :list=" list " @sortChange = " sortChange "></Table>
        <div class="footer">
            <div class="count">
            </div> 
            <el-pagination
                @size-change="handleSizeChange"
                @current-change="handleCurrentChange"
                :current-page="filterForm.page"
                :page-size="filterForm.pagesize"
                :total="total*1"
                >
            </el-pagination>
        </div>
    </div>
</template>
<script>
import Query from './components/query.vue'
import Table from './components/table.vue'
export default {
    data() { 
        return {
            filterForm:{
                page: 1,
                pagesize: 30,
            },
            list:[],
            total:0,
        }
    },
    mounted() {
    },
    methods: {
        getData(filterForm){
            if(filterForm){
                this.filterForm = filterForm
            }
            this.$http.post('/merchant/fund/getFundList',this.filterForm).then((res) => {
                this.list = res.data || []
                this.total = res.total || 0
                
            }).catch((res) => {
                console.log(res)
            })
        },
        sortChange(data){
            if(data.order ){
                this.filterForm.sort = data.prop
                this.filterForm.order = (data.order === "descending"?'desc':'asc')
            }else{
                this.filterForm.sort = ''
                this.filterForm.order =  ''
            }
            this.getData()
        },
        exportExcel(filterForm) {
            if(filterForm){
                this.filterForm = filterForm
            }
            let p = this.$http.post('/merchant/fund/exportFundList',this.filterForm)
            p.then(res => {
                const list = res || []
                require.ensure([], () => {
                    const {
                        export_json_to_excel
                    } = require('../../assets/js/Export2Excel');
                    const tHeader = [
                        '时间',
                    ];
                    const filterVal = [
                        'createdate',
                    ];
                    const data = this.formatJson(filterVal, list);
                    export_json_to_excel(tHeader, data, '记录');
                })
                }).catch(error => {
                return false
            })
        },
        formatJson(filterVal, jsonData) {
            return jsonData.map(v => filterVal.map(j => v[j]))
        },
        handleCurrentChange(val) {
			this.filterForm.page = val
			this.getData()
		},
		handleSizeChange(val) {
			this.filterForm.pagesize = val
			this.getData()
        },
    },
    components: {
        Query,
        Table
    }
}
</script>
<style lang='less' scoped>
.container{
}
</style>