<template>
    <el-table
      :data="list"
      border
      size="mini"
      height="auto"
      stripe
    >
      <el-table-column type="index" width="50" align="center"></el-table-column>
      <el-table-column prop="createdate" label="添加时间"  width="150" align="center"></el-table-column>
      <el-table-column label="操作" width="200" align="center" fixed="right">
        <div slot-scope="scope" v-if="scope.row.OP">
          <el-button
            v-for=" (item,index) in scope.row.OP "
            :key="index"
            type="text"
            @click="btnClick(scope.row,item)"
          >{{ item.btn_name }}</el-button>
        </div>
      </el-table-column>
    </el-table>
</template>
<script>
export default {
  props: {
    list: {
      type: Array,
      default: [],
    },
    tableLoading: {
      type: Boolean,
      default: false,
    },
  },
  data() {
    return {};
  },
  mounted() {},
  methods: {
    btnClick(row, item) {
      console.log(row);
      console.log(item.type);
    }
  },
  components: {},
};
</script>
<style lang='less' scoped>
.table {
}
</style>