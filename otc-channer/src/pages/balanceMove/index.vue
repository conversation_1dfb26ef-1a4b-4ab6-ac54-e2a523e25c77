<template>
    <div class='container'>
        <Query ref="Query" @getData = " getData " @exportExcel = " exportExcel "></Query>
        <Table ref="Table" :list=" list "  ></Table>
        <div class="footer">
            <div class="count">
            </div> 
            <el-pagination
                @size-change="handleSizeChange"
                @current-change="handleCurrentChange"
                :current-page="filterForm.page"
                :page-size="filterForm.pagesize"
                :total="total*1"
                >
            </el-pagination>
        </div>
        <AddItem ref="AddItem"></AddItem>
    </div>
</template>
<script>
import Query from './components/query.vue'
import Table from './components/table.vue'
import AddItem from './components/add.vue'
export default {
    data() { 
        return {
            filterForm:{
                page: 1,
                pagesize: 30,
            },
            list:[],
            total:0,
        }
    },
    mounted() {
        if(JSON.parse(localStorage.getItem("xpay_userInfo")).fund_open===100){ //fund_open：111=允许资金调度 100=禁止资金调度
            this.$router.push('/home')
        }
    },
    methods: {
        getData(filterForm){
            if(filterForm){
                this.filterForm = filterForm
            }
            this.$http.post('/channel/fund/getBalanceMoveList',this.filterForm).then((res) => {
                this.list = res.data || []
                this.total = res.total || 0
                
            }).catch((res) => {
                console.log(res)
            })
        },
        
        exportExcel(filterForm) {
            if(filterForm){
                this.filterForm = filterForm
            }
            let p = this.$http.post('/channel/fund/getBalanceMoveExport',this.filterForm)
            p.then(res => {
                const list = res || []
                require.ensure([], () => {
                    const {
                        export_json_to_excel
                    } = require('../../assets/js/Export2Excel');
                    const tHeader = ['订单号','UID','昵称','类型','币种','金额','汇率','数量','添加时间','调账原因' ] 
                    const filterVal =['id','userid','nickname','type_name','coin_type','money','price','amount','create_time','reason' ]
                    const data = this.formatJson(filterVal, list);
                    export_json_to_excel(tHeader, data, '资金调度');
                })
                }).catch(error => {
                return false
            })
        },
        formatJson(filterVal, jsonData) {
            return jsonData.map(v => filterVal.map(j => v[j]))
        },
        handleCurrentChange(val) {
			this.filterForm.page = val
			this.getData()
		},
		handleSizeChange(val) {
			this.filterForm.pagesize = val
			this.getData()
        },
    },
    components: {
        Query,
        Table,
        AddItem
    }
}
</script>
<style lang='less' scoped>
.container{
}
</style>