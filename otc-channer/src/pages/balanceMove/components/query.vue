<template>
    <el-collapse value="filter">
		<el-collapse-item title="查询条件" name="filter">
            <el-form :inline="true"  ref="filterForm" :model="filterForm" size="mini"> 
                <el-form-item label="订单号:">
                    <el-input v-model="filterForm.id" placeholder="订单号"></el-input>
                </el-form-item>
                <el-form-item label="UID:">
                    <el-input v-model="filterForm.uid" placeholder="UID"></el-input>
                </el-form-item>
                <el-form-item label="币种:">
                    <el-select @change=" getData " v-model="filterForm.coin_type" > 
                        <el-option  v-for="item in coinList" :key="item.value" :label="item.label" :value="item.value"> </el-option>
                    </el-select>
                </el-form-item>
                <el-form-item>
                    <el-button  type="primary" @click.native="getData">查询</el-button>
                    <el-button  type="primary" @click="clear"  >清空</el-button>
                    <el-button  type="primary" @click="exportExcel"  icon="el-icon-document" >导出</el-button>
                    <el-button  type="primary" @click="add"   >创建</el-button>
                </el-form-item>
            </el-form>
        </el-collapse-item>
    </el-collapse>
</template>
<script>
export default {
    data() { 
        return {
            date:[],
            filterForm:{
                coin_type:'',
                id: '',
                uid: '',
                page: 1,
                pagesize: 30,
            },
            coinList:[
                {value:'',label:'全部'},{value:'USDT',label:'USDT'}],
            access:{
                adminInExcel: "N",
            }
        }
    },
    mounted() {
        this.getData()
    },
    methods: {
        add(){
            this.$parent.$refs.AddItem.show()
        },
        clear(){
            this.filterForm = {
                coin_type:'',
                id: '',
                uid: '',
                page: 1,
                pagesize: 30,
            }
            this.$emit('getData',this.filterForm)
        },
        getData(){
            this.filterForm.page = 1 
            this.$emit('getData',this.filterForm)
        },
        exportExcel(){ 
            this.$emit('exportExcel',this.filterForm)
        },
        
    },
    watch: {
    },

    components: {
    }
}
</script>
<style lang='less' scoped>
.query{
}
</style>