<template>
    <el-table
      :data="list"
      border
      size="mini"
      height="auto"
      stripe
    > 
      <el-table-column type="index" width="50" align="center"></el-table-column>
      <el-table-column prop="id" label="订单号"  align="center"></el-table-column>
      <el-table-column prop="userid" label="UID"  align="center"></el-table-column>
      <el-table-column prop="nickname" label="昵称"  align="center"></el-table-column>
      <el-table-column prop="type_name" label="类型"  align="center"></el-table-column>
      <el-table-column prop="coin_type" label="币种"  align="center"></el-table-column>
      <el-table-column prop="money" label="金额"  align="center">
           <div slot-scope="scope" >
             ¥ {{ $tool.BigJs(scope.row.money,1,3,2) }}
          </div>
      </el-table-column>
      <el-table-column prop="price" label="汇率"  align="center">
           <div slot-scope="scope" >
             {{ $tool.BigJs(scope.row.price,1,3,2) }}
          </div>
      </el-table-column>
      <el-table-column prop="amount" label="数量"  align="center"></el-table-column>
      <el-table-column prop="create_time" label="添加时间"  width="150" align="center"></el-table-column>
      <el-table-column prop="reason" label="调账原因"  align="center"></el-table-column>
      
      <!-- <el-table-column label="操作" width="200" align="center" fixed="right">
        <div slot-scope="scope" v-if="sc金额ope.row.OP">
          <el-button
            v-for=" (item,index) in scope.row.OP "
            :key="index"
            type="text"
            @click="btnClick(scope.row,item)"
          >{{ item.btn_name }}</el-button>
        </div>
      </el-table-column> -->
    </el-table>
</template>
<script>
export default {
  props: {
    list: {
      type: Array,
      default: [],
    },
    tableLoading: {
      type: Boolean,
      default: false,
    },
  },
  data() {
    return {};
  },
  mounted() {},
  methods: {
    btnClick(row, item) {
      console.log(row);
      console.log(item.type);
    }
  },
  components: {},
};
</script>
<style lang='less' scoped>
.table {
}
</style>