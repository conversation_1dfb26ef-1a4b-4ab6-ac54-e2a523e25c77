<template>
    <el-dialog title='资金调度'  width='400px' :visible.sync='widgetVisible' :close-on-click-modal=' false ' class='dialog' :center='true' >
        <div>
            <el-form inline label-width="80px" size="mini"> 
                <el-form-item  label="类型:">
                    <el-select   v-model="filterForm.optType" placeholder="请选择类型" > 
                        <el-option  v-for="item in typeList" :key="item.value" :label="item.label" :value="item.value"> </el-option>
                    </el-select>
                </el-form-item>
                <el-form-item label="UID:">
                    <el-input :disabled="!isAdd" v-model="filterForm.uid" placeholder="uid"><el-button slot="append" v-if="isAdd"  @click="getSubUserBalance()">查询资产</el-button></el-input>
                </el-form-item> 
                <el-form-item label="余额:" v-if="filterForm.balance>0">
                    <el-input v-model="filterForm.balance" disabled> </el-input>
                </el-form-item>
                
                <el-form-item label="调账数量:">
                    <el-input v-model="filterForm.amount" @input="amountInput" placeholder="调账数量"> </el-input>
                </el-form-item>
                <el-form-item label="调账金额:" >
                    <el-input v-model="filterForm.money" @input="moneyInput" placeholder="调账金额"><span slot="prepend"  >¥</span></el-input>
                </el-form-item>
                 <el-form-item  label="调账原因:">
                    <el-select   v-model="filterForm.reason" placeholder="请选择调账原因" > 
                        <el-option  v-for="item in reasonList" :key="item.value" :label="item.label" :value="item.value"> </el-option>
                    </el-select>
                </el-form-item>
            </el-form>
        </div>
        <div slot='footer' class='dialog-footer'>
            <el-button type=''  size='small' @click='widgetVisible=false'>取消</el-button>
            <el-button type='primary'  size='small' @click='addBalanceMove'>提交</el-button>
        </div>
    </el-dialog>
</template>
<script>
export default {
    data() { 
        return {
            widgetVisible:false,
            filterForm:{
                uid:'',
                amount:'',
                optType:'',
                reason:'',
                price:1,
                balance:0,
            },
            isAdd:true,
            typeList:[
                {value:1,label:'系统补款'},{value:2,label:'系统扣款'}, 
            ],
            reasonList:[
                {value:'保证金充值',label:'保证金充值'},  
                {value:'业绩奖励',label:'业绩奖励'}, 
                {value:'核算扣币',label:'核算扣币'}, 
                {value:'其他',label:'其他'}, 
            ]
        }
    },
    mounted() {
    },
    methods: {
        show(data){ 
            if(!data){ 
                this.isAdd = true
                this.filterForm={
                    uid:'',
                    amount:'',
                    optType:'',
                    reason:'',
                    price:1,
                    balance:0,
                    money:''
                }
                
            }else{
               console.log(data)
               this.isAdd = false
               this.filterForm={
                    uid:data.userid,
                    amount:'',
                    optType:'',
                    reason:'',
                    price:1,
                    balance:data.can_use_money_usdt,
                    money:''
                }
            }
            this.getPrice()
            this.widgetVisible = true
            
        },
        amountInput(){
            this.filterForm.amount = this.filterForm.amount.replace(/[^\d.]/g,'')
            this.filterForm.amount =  this.filterForm.amount.match(/^\d*(\.?\d{0,4})/g)[0] 
            this.filterForm.money = Math.floor(this.filterForm.amount*this.filterForm.price*100)/100
        },
        moneyInput(){
            this.filterForm.money = this.filterForm.money.replace(/[^\d.]/g,'')
            this.filterForm.money =  this.filterForm.money.match(/^\d*(\.?\d{0,2})/g)[0] 
            this.filterForm.amount = Math.floor(this.filterForm.money/this.filterForm.price*1000)/1000
        },
        getSubUserBalance(){ 
            if(!this.filterForm.uid){
                this.$message('请输入uid')
                return
            }
            this.$http.post('/channel/fund/getSubUserBalance',{uid:this.filterForm.uid}).then((res) => { 
                this.filterForm.balance = res  
            }).catch((err) => { 
             console.log(err)
            })
        },
        addBalanceMove(){

             if(!this.filterForm.optType){
                this.$message('请选择调度类型')
                return
            }
             if(!this.filterForm.uid){
                this.$message('请输入uid')
                return
            }
             if(!this.filterForm.amount){
                this.$message('请输入调度数量')
                return
            }
             if(!this.filterForm.reason){
                this.$message('请选择调度原因')
                return
            }
            this.$http.post('/channel/fund/addBalanceMove',{
                uid:this.filterForm.uid,//：UID
                optType:this.filterForm.optType,//：类型，系统补款=1，系统扣款=2
                amount:this.filterForm.amount,//：调度数量
                reason:this.filterForm.reason,//：调度原因
            }).then((res) => {
                this.$parent.getData()
                this.$message.success('提交成功')
                this.widgetVisible = false
            }).catch((err) => { 
             console.log(err)
            })
        },
        getPrice(){
            this.$http.post('/channel/fund/getPrice',{}).then((res) => { 
                this.filterForm.price = res*1  
            }).catch((err) => { 
             console.log(err)
            })
        }
    },
    components: {
    }
}
</script>
<style lang='less' scoped>
/deep/.el-dialog__body{
    .el-form-item{
        margin-top: 10px;
    }
    .el-form-item__content{
        width: 210px;
    }
    .el-input__inner{
        background: #fff !important;
        
    }
    .el-input-group__append button.el-button{
            background: #66b1ff;
            border-color: #66b1ff;
            color: #fff;
        }
    .el-input.is-disabled .el-input__inner{
        background-color: #F5F7FA !important;
    }
} 
</style>