<template>
    <el-dialog title="提币" width="500px" :visible.sync="dialogForm">
      <el-form :model="form">
        <el-form-item label="币种:" label-width="100px">
          <el-select v-model="form.currencyType" style="width:100%" size="small" placeholder="请选择">
            <el-option label="USDT" value="USDT"></el-option>
          </el-select>
        </el-form-item> 
        <el-form-item label="链类型:" label-width="100px"> 
            <el-select v-model="form.addrtype" style="width:100%" size="small" placeholder="请选择">
                <el-option label="ERC-20" value="101"></el-option>
                <el-option label="TRC-20" value="102"></el-option> 
            </el-select>
          
        </el-form-item> 
        
        <el-form-item label="可用余额:" label-width="100px">
          <el-input size="small" type="text" :readonly="true" :disabled="true" v-model="createOrderInfo.balance" placeholder="可用余额"></el-input>
        </el-form-item>

        <el-form-item label="提币地址:" label-width="100px">
          <el-input size="small" type="text"  v-model="form.toaddress" placeholder="提币地址"></el-input>
        </el-form-item>
        <el-form-item label="提币数量:" label-width="100px">
          <el-input size="small" type="text"  v-model="form.currencyAmount" placeholder="提现数量"></el-input>
        </el-form-item>
        <el-form-item label="提币手续费:" label-width="100px">
          <el-input size="small" type="text" :disabled="true" v-model="form.feeCount" placeholder="手续费"></el-input>
        </el-form-item>
        
        <el-form-item label="资金密码:" label-width="100px">
          <el-input size="small" type="password" v-model="form.fundPwd" placeholder="资金密码"></el-input>
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <!-- <el-button type="info" size="mini" @click="dialogForm=false">取 消</el-button> -->
        <el-button type="primary" size="mini" @click="setSure">提  交</el-button>
      </div>
    </el-dialog>
</template>


<script>
export default {
    props: {
        createOrderInfo: {
            default: {}
        }
    },
    data(){
        return {
            dialogForm: false,
            form: {
                toaddress:'',
                addrtype:'',
                currencyAmount:'',
                feeCount:'',
                fundPwd:'',
                currencyType:'USDT',
            },
        }
    },
    watch:{
      'form.currencyAmount'(n, o){
          let reg = /^[0-9]{1,10}(\.|(\.\d{1,2}))?$/
          if(!n) return 
          if(!reg.test(n)){ 
            return this.form.currencyAmount = o
          }
          
      },
    },
    methods: {
        show(){
            setTimeout(()=>{
                this.form.feeCount = this.createOrderInfo.expense
            },100)
            this.form = {
                toaddress:'',
                currencyAmount:'',
                feeCount:'',
                fundPwd:'',
                currencyType:'USDT',
            }
            this.dialogForm = true
        },
        
        setSure(){
            if(this.createOrderInfo.balance < this.createOrderInfo.expense){
                this.$message('可用余额不足')
                return
            }
            if(!this.form.addrtype){
                this.$message('请选择链类型')
                return
            }
            
            if(!this.form.currencyAmount){
                this.$message({
                showClose: true,
                message: `输入提现金额`,
                type: 'error'
                });
                return
            }else if(this.form.currencyAmount*1 >  (this.createOrderInfo.balance - this.createOrderInfo.expense)){
                this.form.currencyAmount = Math.floor((this.createOrderInfo.balance - this.createOrderInfo.expense)*10000)/10000
                this.$message('扣除手续费后最多可提币'+this.form.currencyAmount + this.form.currencyType)
                return 
            }
            if(!this.form.toaddress){
                this.$message({
                showClose: true,
                message: `请输入提币地址`,
                type: 'error'
                });
                return
            }
           
            if(!this.form.fundPwd){
                this.$message({
                showClose: true,
                message: `请输入资金密码`,
                type: 'error'
                });
                return
            } 
            this.dialogForm = false 
            this.$confirm("您确定提现吗？", "提示", {
                confirmButtonText: "确认",
                cancelButtonText: "取消",
                type: "warning"
            }).then(() => {
                this.apply();
            }).catch(() => {});
        },
        apply(){
            this.$http.get('/channel/fund/subWithdraw',this.form).then(res => {
                this.$notify({
                title: "提示",
                message: `提交成功！请等待审核`,
                type: "success"
                });
                this.$parent.getList();
                this.dialogForm = false;
            }).catch(()=>{
                // this.$notify({
                // title: "提示",
                // message: `提交失败！请重试`,
                // type: "error"
                // });
                // this.dialogForm = false;
            });
        }
        
        
    }

}
</script>


<style lang="less" scoped>
    
</style>