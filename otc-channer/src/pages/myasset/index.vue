<template>
  <div>
    <div class="searchHeader">
      <div>  总资产折合：<span style="color:red">¥ {{$tool.BigJs(total.total_rmb,1,3,2)  }}  </span> ≈  {{$tool.BigJs(total.total_usdt,1,3,4)  }} USDT  
        <!-- <el-button size="mini" style="margin-left: 30px"  type="primary" @click="crwithDraw" v-show="fund_open==100">提币</el-button>
        <el-button size="mini" style="margin-left: 30px"  type="primary" @click="openTransfer">转账</el-button> -->
      </div>
    </div>

    <div class="tableList">
      <el-table :data="tableData" border style="width: 100%" size="mini"
        :row-style="{'font-size':'12px','color':'#324253'}"
        :header-row-style="{'font-size':'12px','color':'#1f2021'}">
        <el-table-column align="center" prop="coin_name" label="币种" width=""></el-table-column>
        <el-table-column align="center" prop="number_over" label="可用" :formatter="USDTAmountFormat"></el-table-column>
        <el-table-column align="center" prop="number_lock" label="冻结" :formatter="USDTAmountFormat" width=""></el-table-column>
        <el-table-column align="center" prop="expense" label="汇率" width="" :formatter="RMBAmountFormat"></el-table-column>
        <el-table-column align="center" prop="number_cny" label="泰铢估值" :formatter="RMBAmountFormat" width="" >
             <div slot-scope="scope" >
               ¥{{ $tool.BigJs(scope.row.number_cny,1,3,2) }}
            </div>
        </el-table-column>
      </el-table>
    </div>

    <SetFundPwd ref="SetFundPwd"></SetFundPwd>
    <WithDraw :createOrderInfo = "createOrderInfo" ref="WithDraw"></WithDraw>


    <!-- 转账 -->
    <el-dialog title='转账'  width='500px' :visible.sync='transfer_show' class='dialog' >
      <el-form ref="form" :model="transfer_form" label-width="90px">
        <el-form-item label="币种">
          <div>USDT</div>
        </el-form-item>
        <el-form-item label="可用余额：">
          <el-input v-model="balance" :disabled="true"></el-input>
        </el-form-item>
        <el-form-item label="转账地址：" style="marginTop: 10px;">
          <el-input v-model="transfer_form.toUserIDList" placeholder="请输入用户编码" @blur="userNameBlur">
            <template slot="append">{{transfer_form.nikename}}</template>
          </el-input>
        </el-form-item>
        <el-form-item label="转账数量：" style="marginTop: 10px;">
          <el-input v-model="transfer_form.amountList" oninput=" value = value.replace(/[^\d.]/g,'').match(/^\d*(\.?\d{0,4})/g)[0] " placeholder="请输入转账数量"></el-input>
        </el-form-item>
        <el-form-item label="手续费：" style="marginTop: 10px;">
          <el-input v-model="transfer_curreny_rate" :disabled="true"></el-input>
        </el-form-item>
        <el-form-item label="资金密码" style="marginTop: 10px;">
          <el-input v-model="transfer_form.fundPwd" type="password" placeholder="请输入资金密码" size="small"></el-input>
        </el-form-item>
      </el-form>
      <div slot='footer' class='dialog-footer'>
        <el-button type=''  size='small' @click='transfer_show=false'>取消</el-button>
        <el-button type='primary'  size='small' @click='confirmTransfer'>提交</el-button>
      </div>
    </el-dialog>

    
  </div>
</template>

<script>
import { mapState } from 'vuex';
import SetFundPwd from '../accountInfo/components/setFundPwd' 
import WithDraw from './components/withDraw' 

export default {
  components:{
    SetFundPwd,
    WithDraw
  },
  name: "",
  data: function() {
    return {
      tableData: [],
      total: {},
      createOrderInfo: {},
      createBBInfo: {},
      form: {},
      dialogForm: false, 
      fund_open:111,

      // 转账
      transfer_show: false,
      balance: 0,
      transfer_curreny_rate: 0,
      transfer_form: {
        toUserIDList: '',
        amountList: '',
        fundPwd: '',
        nikename: ''
      }
    };
  },
  mounted() {
    this.getBlank();
    this.getList(); 
    this.fund_open=JSON.parse(localStorage.getItem("xpay_userInfo")).fund_open
  },
  
  computed: {
    ...mapState(['userSet'])
  },
  methods: {
    getBlank() {
        this.$http.post('/channel/index/home', {},).then(res=>{
          this.balance = res.balance.can_use_balance
          this.transfer_curreny_rate = res.changeRate.transfer_curreny_rate
        })
    },
    getList(){
      this.$http.get('/channel/user/index', {}).then(res=>{
        this.tableData = res.data;
        this.total = {
          total_rmb: res.total_rmb,
          total_usdt: res.total_usdt
        }
      })
    },
    USDTAmountFormat(row, column,cellValue, index){
      return this.$tool.Fslice(cellValue) 
    },
    RMBAmountFormat(row, column,cellValue, index){
      return this.$tool.Tslice(cellValue) 
    },
    crwithDraw(){
      this.createOrderInfo = {}; 
      if(this.userSet.money_pwd_seted == '111'){
        this.$http.get('/channel/fund/withdraw', {}).then(res=>{
          this.createOrderInfo = res;
          this.$refs.WithDraw.show()
        })
      }else{
        this.$confirm('请设置资金密码').then(()=>{
          this.$refs.SetFundPwd.show(1,this.userSet.account) ////1:第一次设置密码 2:修改资金密码
        }).catch(()=>{

        })
      }
    },

    // 转账
    openTransfer() {
      this.transfer_show = true
      this.transfer_form = {
        toUserIDList: '',
        amountList: '',
        fundPwd: '',
        nikename: ''
      }
    },
    userNameBlur(){
        if(!this.transfer_form.toUserIDList){return}
        this.$http.post('/channel/fund/getNickname',{uid:this.transfer_form.toUserIDList}).then((res) => {
            // this.isGoogel = res
            console.log(res);
          this.transfer_form.nikename = res
        }).catch((err) => { 
          console.log(err)
        })
    },
    confirmTransfer() {
      if (this.transfer_form.toUserIDList.trim() === '') {
        this.$message('请输转账地址')
        return
      }
      if (this.transfer_form.amountList.trim() === '') {
        this.$message('请输转账数量')
        return
      }
      if (this.transfer_form.fundPwd.trim() === '') {
        this.$message('请输资金密码')
        return
      }


      this.$http.post('/channel/fund/alltransfer', {
        toUserIDList: this.transfer_form.toUserIDList.trim(),
        amountList: this.transfer_form.amountList.trim(),
        fundPwd: this.transfer_form.fundPwd.trim()
      }).then((res) => {
          this.$message.success('转账成功')
          this.getBlank()
          this.getList()
          this.transfer_show = false
      }).catch((err) => { 
          console.log(err)
      })
    }
  },
  
};
</script>

<style scoped lang="less">

</style>
