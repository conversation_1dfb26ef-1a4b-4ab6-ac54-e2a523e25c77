<template>
    <div class='container'>
        <Query ref="Query" @getData = " getData "  @exportExcel = " exportExcel "></Query>
        <Table ref="Table" :list=" list "  :paytypeList = "paytypeList" ></Table>
        <div class="footer">
            <div class="count">
            </div> 
            <el-pagination
                @size-change="handleSizeChange"
                @current-change="handleCurrentChange"
                :current-page="filterForm.page"
                :page-size="filterForm.pagesize"
                :total="total*1"
                >
            </el-pagination>
        </div>
        <AddBalanceMove ref="AddBalanceMove"></AddBalanceMove>
        <ModifyPay ref="ModifyPay"></ModifyPay>
        
    </div>
</template>
<script>
import Query from './components/query.vue'
import Table from './components/table.vue'
import AddBalanceMove from '../balanceMove/components/add.vue'
import ModifyPay from './components/modifyPay.vue'

export default {
    data() { 
        return {
            filterForm:{
                page: 1,
                pagesize: 30,
            },
            list:[],
            total:0,
            paytypeList:[{name:'8201', label:'银行卡转卡'}]
        }
    },
    mounted() {
    },
    methods: {
        getData(filterForm){
            if(filterForm){
                this.filterForm = filterForm
            }
            this.$http.post('/channel/Paymethod/getPayMethodList',this.filterForm).then((res) => {
                this.list = res.data || []
                this.total = res.total || 0 
                if(this.paytypeList.length===1){
                    for (const key in res.pay_type) {
                        if(key!=8201){
                            /* this.paytypeList.push({
                                name:key,
                                label:res.pay_type[key]
                            }) */
                        }
                        
                    }
                }
                
            }).catch((res) => {
                console.log(res)
            })
        }, 
        exportExcel(filterForm) {
            if(filterForm){
                this.filterForm = filterForm
            }
            let p = this.$http.post('/channel/fund/getPayOrderExport',this.filterForm)
            p.then(res => {
                const list = res || []
                require.ensure([], () => {
                    const {
                        export_json_to_excel
                    } = require('../../assets/js/Export2Excel');
                    const tHeader = ['平台单号','币种','订单金额','汇率','交易数量','收款方昵称','收款账号','收款账号类型','状态','付款方类型','付款方昵称','下单时间']
      
                    const filterVal = ['id','coin_type','money','price','amount', 'sellnickname','payee_account','pay_type_name','status_name','payusertype_name','buynickname','create_time']
                    const data = this.formatJson(filterVal, list);
                    export_json_to_excel(tHeader, data, '充值记录');
                })
                }).catch(error => {
                return false
            })
        },
        formatJson(filterVal, jsonData) {
            return jsonData.map(v => filterVal.map(j => v[j]))
        },
        handleCurrentChange(val) {
			this.filterForm.page = val
			this.getData()
		},
		handleSizeChange(val) {
			this.filterForm.pagesize = val
			this.getData()
        },
    },
    components: {
        Query,
        Table,
        AddBalanceMove,
        ModifyPay
    }
}
</script>
<style lang='less' scoped>
.container{
}
</style>