<template>
    <el-dialog title='修改限额'  width='400px' :visible.sync='widgetVisible' :close-on-click-modal=' false ' class='dialog' :center='true' >
        <div>
            <el-form inline label-width="100px" size="mini">
                <el-form-item label="币商编号:" > <el-input v-model="formData.userid" disabled ></el-input> </el-form-item>
                <el-form-item label="昵称:" > <el-input v-model="nickname" disabled ></el-input> </el-form-item>
                <el-form-item label="收款名称:" > <el-input v-model="name" disabled ></el-input> </el-form-item>
                <el-form-item label="类型:" > <el-input v-model="pay_type_name" disabled ></el-input> </el-form-item>
                <el-form-item label="收款账号:" > <el-input v-model="account" disabled ></el-input> </el-form-item> 
                <el-form-item label="单日限额:" > <el-input v-model="formData.daylimit"  ></el-input> </el-form-item> 
                <el-form-item label="单笔限额:" > <el-input v-model="formData.singlelimit"  ></el-input> </el-form-item> 
            </el-form>
        </div>
        <div slot='footer' class='dialog-footer'>
            <el-button type=''  size='small' @click='widgetVisible=false'>取消</el-button>
            <el-button type='primary'  size='small' @click='modifyLimitPay'>提交</el-button>
        </div>
    </el-dialog>
</template>
<script>
export default {
 data() { 
    return {
        widgetVisible:false,
        formData:{
            userid:'',//：币商ID
            payId:'',//：支付方式ID
            singlelimit:'',//：单笔限额
            daylimit:'',//：单日限额
        },
        nickname:'',
        name:'',
        account:'',
        pay_type_name:'',
    }
 },
 mounted() {
 },
 methods: {
    show(data){ 
        console.log(data)
        
        this.formData = {
            userid:data.userid,//：币商ID
            payId:data.id,//：支付方式ID
            singlelimit:data.single_limit,//：单笔限额
            daylimit:data.day_limit,//：单日限额
        }
        this.nickname = data.nickname
        this.name = data.name
        this.account= data.account
        this.pay_type_name = data.pay_type_name
        this.widgetVisible = true
    },
    modifyLimitPay(){
        this.$http.post('channel/paymethod/modifyLimitPay',this.formData).then((res) => {
          this.$message.success('修改成功')
          this.widgetVisible = false
          this.$parent.getData()
        }).catch((err) => { 
         console.log(err)
        })
    },
 },
 components: {
 }
}
</script>
<style lang='less' scoped>
/deep/.el-dialog__body{
    .el-form-item{
        margin-top: 10px;
    }
    .el-form-item__content{
        width: 210px;
    }
    .el-input__inner{
        background: #fff !important;
        
    }
    .el-input-group__append button.el-button{
            background: #66b1ff;
            border-color: #66b1ff;
            color: #fff;
        }
    .el-input.is-disabled .el-input__inner{
        background-color: #F5F7FA !important;
        color: #333;
    }
} 
</style>