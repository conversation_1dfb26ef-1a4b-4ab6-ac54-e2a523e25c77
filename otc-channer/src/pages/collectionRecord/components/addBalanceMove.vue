<template>
    <el-dialog title='创建资金调度'  width='400px' :visible.sync='widgetVisible' :close-on-click-modal=' false ' class='dialog' :center='true' >
        <div></div>
        <div slot='footer' class='dialog-footer'>
            <el-button type=''  size='small' @click='widgetVisible=false'>取消</el-button>
            <el-button type='primary'  size='small' @click='widgetVisible=false'>提交</el-button>
        </div>
    </el-dialog>
</template>
<script>
export default {
 data() { 
  return {
   widgetVisible:false
  }
 },
 mounted() {
 },
 methods: {
  show(){ this.widgetVisible = true }
 },
 components: {
 }
}
</script>
<style lang='less' scoped>
.dialog{
}
</style>