<template>
    <div class="box">
        <el-tabs v-model="activeName"  @tab-click="handleClick">
            <el-tab-pane :label="item.label" :name="item.name" v-for=" (item,index) in paytypeList " :key="index"></el-tab-pane>  
        </el-tabs>

        <el-table
        :data="list"
        border
        size="mini"
        height="auto"
        stripe >
            <el-table-column type="index" width="50" align="center"></el-table-column> 
            <el-table-column prop="userid" label="币商编号"   align="center"></el-table-column>
            <el-table-column prop="nickname" label="币商昵称"   align="center"></el-table-column>
            <el-table-column prop="name" label="收款名称" show-overflow-tooltip   align="center"></el-table-column>
            <el-table-column prop="account" label="收款账号" show-overflow-tooltip  align="center"></el-table-column> 
            <el-table-column prop="pay_type_name" label="类型"  width="120"  align="center"></el-table-column> 
            <el-table-column prop="can_use_money" label="账户余额"  width="280"  align="center">
                <div slot-scope="scope" > 
                    <p>¥{{ $tool.BigJs(scope.row.can_use_money_cny,1,3,2) }}</p>
                    <p>{{ $tool.BigJs(scope.row.can_use_money_usdt,1,3,4) }} USDT <i v-if="fund_open===111" @click="addBalanceMove(scope.row)" class="btn el-icon-edit-outline"></i></p>
                </div>
            </el-table-column> 
            
            
            <el-table-column prop="day_deal_money" label="今日收款"   align="center" show-overflow-tooltip>
                <div slot-scope="scope" > ¥{{ $tool.BigJs(scope.row.day_deal_money,1,3,2) }} </div>
            </el-table-column> 
            <el-table-column prop="sell_comm_rate" label="费率"    align="center">
                <div slot-scope="scope" > {{ $tool.BigJs(scope.row.sell_comm_rate,100,3,2) }}% </div>
            </el-table-column> 
            <!-- <el-table-column prop="sell_comm_rate" label="收益"    align="center">
                <div slot-scope="scope" > 待开发 </div>
            </el-table-column>  -->
            <el-table-column prop="day_limit" label="限额"  width="200" align="center">
                <div slot-scope="scope">
                    <p>单日: ¥{{ scope.row.day_limit }}</p>    
                    <p>单笔: ¥{{ scope.row.single_limit }} <i v-show="scope.row.prohibit_login!=0"  @click="modifyPay(scope.row)" class="btn el-icon-edit-outline"></i></p>
                </div>    
            </el-table-column> 
            <el-table-column prop="status" label="状态"   align="center"> 
                <div slot-scope="scope">
                    <el-switch :value=" scope.row.status == 1 " @change=" setStatus(scope.row) " active-color="#13ce66" inactive-color="#ff4949"> </el-switch>
                </div> 
            </el-table-column>  
            <!-- <el-table-column prop="prohibit_login" label="登陆"  width="150" align="center">
                <div slot-scope="scope" >
                {{scope.row.prohibit_login}}{{  scope.row.prohibit_login==0?'允许登陆':'禁止登陆'  }}
                </div>
            </el-table-column> -->
            <el-table-column prop="create_time" label="创建时间"  width="150" align="center"></el-table-column>
        </el-table>
    </div>
</template>
<script>
export default {
  props: {
    list: {
      type: Array,
      default: [],
    },
    tableLoading: {
      type: Boolean,
      default: false,
    },
    paytypeList:{
        type:Array,
        default:()=>[ ] 
    }
  }, 
  data() {
    return { 
        activeName:'8201' ,
        fund_open:100,  
    };
  },
  mounted() {
      this.fund_open=JSON.parse(localStorage.getItem("xpay_userInfo")).fund_open
  },
  methods: {
    btnClick(row, item) {
      console.log(row);
      console.log(item.type);
    },
    setStatus(data){ 
         if(data.prohibit_login==0){
            this.$message('可登录状态无法修改')
            return
        }
        this.$http.post('/channel/paymethod/setPaySwitch',{
            userid:data.userid,//币商ID
            payId:data.id,//支付方式ID
            switch:data.status==1?0:1,//0 失效 1正常
        }).then((res) => {
         this.$message.success('切换成功')
         this.$parent.getData()
        }).catch((err) => { 
         console.log(err)
        })
    },
    handleClick(){
        console.log(this.activeName)
        this.$parent.$refs.Query.getData({paytype:this.activeName})
    },
    addBalanceMove(data){
        this.$parent.$refs.AddBalanceMove.show(data)
    },
    modifyPay(data){
        if(data.prohibit_login==0){
            this.$message('可登录状态无法修改')
            return
        }
        this.$parent.$refs.ModifyPay.show(data)
    }
  },
  components: {},
};
</script>
<style lang='less' scoped>
.box {
    flex: 1;
    width: 100%;
    display: flex;
    flex-direction: column; 
    box-sizing: border-box;
    padding:  0 ;
    /deep/ .el-tabs__header {
            margin: 0;
        }
}
.btn{
    color: #66b1ff;
    cursor: pointer;
}
</style>