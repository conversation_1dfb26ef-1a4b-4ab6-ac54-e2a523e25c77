<template>
    <el-collapse value="filter">
		<el-collapse-item title="查询条件" name="filter">
            <el-form :inline="true"  ref="filterForm" :model="filterForm" size="mini"> 
                <el-form-item label="币商编号:">
                    <el-input v-model="filterForm.userid" placeholder="币商编号"></el-input>
                </el-form-item>
                <el-form-item label="币商昵称:">
                    <el-input v-model="filterForm.nickname" placeholder="币商昵称"></el-input>
                </el-form-item>
                <el-form-item label="收款人姓名:">
                    <el-input v-model="filterForm.name" placeholder="收款人姓名"></el-input>
                </el-form-item>
                <el-form-item label="账号:">
                    <el-input v-model="filterForm.account" placeholder="账号"></el-input>
                </el-form-item>
                
                <el-form-item label="状态:">
                    <el-select @change=" getData " v-model="filterForm.status" placeholder="选择状态">
                        <el-option
                            v-for="item in statusList"
                            :key="item.value"
                            :label="item.label"
                            :value="item.value">
                        </el-option>
                    </el-select>
                </el-form-item> 
                <el-form-item>
                    <el-button  type="primary" @click.native="getData">查询</el-button>
                    <el-button  type="primary" @click="clear"  >清空</el-button>
                </el-form-item>
            </el-form>
        </el-collapse-item>
    </el-collapse>
</template>
<script>
export default {
    props:{ 
    },
    data() { 
        return {
            date:[],
            filterForm:{ 
                userid:'',
                nickname:'',
                name: '',
                account: '',
                status: '',
                page: 1,
                pagesize: 30,
                paytype:'8201'
            },
            statusList:[  {value:'',label:'全部'} ,{value:0,label:'未启用'}, {value:1,label:'已启用'}], 
        }
    },
    mounted() {
        this.getData()
    },
    methods: {
        clear(){
            this.filterForm = {
                userid:'',
                nickname:'',
                name: '',
                account: '',
                status: '',
                page: 1,
                pagesize: 30,
                paytype:'8201'
            }
            this.$emit('getData',this.filterForm)
        },
        getData(data){
            if(data&&data.paytype ){
                
                if(this.filterForm.paytype === data.paytype){
                    return
                }
                this.filterForm.paytype = data.paytype
            }
            this.filterForm.page = 1
            this.setDate()
            this.$emit('getData',this.filterForm)
        },
        exportExcel(){
            this.setDate()
            this.$emit('exportExcel',this.filterForm)
        },
        setDate(){
            if(this.date&&this.date.length>0){
                this.filterForm.start_time = this.date[0]+' 00:00:00'
                this.filterForm.end_time = this.date[1]+' 23:59:59'
            }else{
                this.filterForm.start_time = ''
                this.filterForm.end_time = ''
            }
        }
    },
    watch: {
    },

    components: {
    }
}
</script>
<style lang='less' scoped>
.query{
}
</style>