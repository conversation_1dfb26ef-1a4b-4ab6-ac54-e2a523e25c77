<template>
    <el-table
      :data="list"
      border
      size="mini"
      height="auto"
      stripe
      ref="multipleTable"
    >
        <el-table-column type="index" width="50" align="center"></el-table-column>
        <el-table-column prop="create_time" label="创建时间"  width="150" align="center"></el-table-column>
        <el-table-column label="类型"  align="center">
          <div slot-scope="scope" >
                {{scope.row.type === 'in' ? '转入' : '转出'}}
          </div>
        </el-table-column>
        <el-table-column prop="coin_type" label="币种"  align="center"></el-table-column>
        <el-table-column prop="amount" label="数量(USDT)"  width="250" align="center">
            <div slot-scope="scope" >
                {{ $tool.BigJs(scope.row.amount,1,3,4) }}
            </div>
        </el-table-column>
        <el-table-column prop="tx_fee" label="手续费(USDT)"  width="250" align="center">
             <div slot-scope="scope" >
                {{ $tool.BigJs(scope.row.tx_fee,1,3,4) }}
            </div>
        </el-table-column>
        <el-table-column prop="get_amount" label="到账数量(USDT)"  width="250" align="center">
            <div slot-scope="scope" >
                {{ $tool.BigJs(scope.row.get_amount,1,3,4) }}
            </div>
        </el-table-column>
        <el-table-column prop="status_name" label="状态" width="150" align="center"></el-table-column>
        <el-table-column label="FROM编码/昵称" width="250" align="center">
          <div slot-scope="scope">
            {{scope.row.userid + '/' + scope.row.nickename}}
          </div>
        </el-table-column>
        <el-table-column label="TO编码/昵称" width="250" align="center">
          <div slot-scope="scope">
            {{scope.row.to_userid + '/' + scope.row.to_nickname}}
          </div>
        </el-table-column>
        

        <el-table-column prop="id" label="订单号"  align="center"></el-table-column>
        
      <!-- <el-table-column label="操作" width="200" align="center" fixed="right">
        <div slot-scope="scope" v-if="scope.row.OP">
          <el-button
            v-for=" (item,index) in scope.row.OP "
            :key="index"
            type="text"
            @click="btnClick(scope.row,item)"
          >{{ item.btn_name }}</el-button>
        </div>
      </el-table-column> -->
    </el-table>
</template>
<script>
export default {
  props: {
    list: {
      type: Array,
      default: [],
    },
    tableLoading: {
      type: Boolean,
      default: false,
    },
  },
  data() {
    return {};
  },
  mounted() {},
  methods: {
    btnClick(row, item) {
      console.log(row);
      console.log(item.type);
    }
  },
  components: {},
};
</script>
<style lang='less' scoped>
.table {
}
</style>