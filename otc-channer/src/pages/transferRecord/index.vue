<template>
    <div class='container'>
        <Query ref="Query" @getData = " getData " @exportExcel = " exportExcel "></Query>
        <Table ref="Table" :list=" list " @sortChange = " sortChange "></Table>
        <div class="footer">
            <div class="count" style="display: flex;">
                <div style="marginRight: 10px;">
                  <p> 当前页转账数量:{{$tool.BigJs(pageAmount,1,3,4)}} USDT</p>
                  <p> 总转账数量:{{$tool.BigJs(amountSum,1,3,4)}} USDT</p>
                </div>
                <div style="marginRight: 10px;">
                  <p> 当前页手续费:{{$tool.BigJs(pageFee,1,3,4)}} USDT</p>
                  <p> 总手续费:{{$tool.BigJs(feeSum,1,3,4)}} USDT</p>
                </div>
                <div>
                  <p> 当前页到账数量:{{$tool.BigJs(pageGetamount,1,3,4)}} USDT</p>
                  <p> 总到账数量:{{$tool.BigJs(getamountSum,1,3,4)}} USDT</p>
                </div>
            </div>
            <el-pagination
                @size-change="handleSizeChange"
                @current-change="handleCurrentChange"
                :current-page="filterForm.page"
                :page-size="filterForm.pagesize"
                :total="total*1"
                >
            </el-pagination>
        </div>
    </div>
</template>
<script>
import Query from './components/query.vue'
import Table from './components/table.vue'
export default {
    data() { 
        return {
            filterForm:{
                page: 1,
                pagesize: 30,
            },
            list:[],
            total:0,
            pageAmount: 0,
            pageFee: 0,
            pageGetamount: 0,
            amountSum: 0,
            feeSum: 0,
            getamountSum: 0,
        }
    },
    mounted() {
    },
    methods: {
        getData(filterForm){
            if(filterForm){
                this.filterForm = filterForm
            }
            this.$http.post('/channel/fund/chainTransList',this.filterForm).then((res) => {
                this.list = res.data || []
                this.total = res.total || 0

                this.pageAmount = res.pageAmount || 0
                this.pageFee= res.pageFee || 0
                this.pageGetamount = res.pageGetamount || 0
                this.amountSum = res.amountSum || 0
                this.feeSum = res.feeSum || 0
                this.getamountSum = res.getamountSum || 0
                
            }).catch((res) => {
                console.log(res)
            })
        },
        sortChange(data){
            if(data.order ){
                this.filterForm.sort = data.prop
                this.filterForm.order = (data.order === "descending"?'desc':'asc')
            }else{
                this.filterForm.sort = ''
                this.filterForm.order =  ''
            }
            this.getData()
        },
        exportExcel(filterForm) {
            if(filterForm){
                this.filterForm = filterForm
            }
            let p = this.$http.post('/channel/fund/exportChainTransList',this.filterForm)
            p.then(res => {
                const list = res || []
                require.ensure([], () => {
                    const {
                        export_json_to_excel
                    } = require('../../assets/js/Export2Excel');
                    const tHeader = ['创建时间','币种','数量','手续费','到账数量','状态','FROM编码','FROM昵称', 'TO编码', 'TO昵称']
                    const filterVal = ['create_time','coin_type','amount','tx_fee','get_amount','status_name','userid','nickename', 'to_userid', 'to_nickname']
                    const data = this.formatJson(filterVal, list);
                    export_json_to_excel(tHeader, data, '转账记录');
                })
                }).catch(error => {
                return false
            })
        },
        formatJson(filterVal, jsonData) {
            return jsonData.map(v => filterVal.map(j => v[j]))
        },
        handleCurrentChange(val) {
			this.filterForm.page = val
			this.getData()
		},
		handleSizeChange(val) {
			this.filterForm.pagesize = val
			this.getData()
        },
    },
    components: {
        Query,
        Table
    }
}
</script>
<style lang='less' scoped>
.container{
}
</style>