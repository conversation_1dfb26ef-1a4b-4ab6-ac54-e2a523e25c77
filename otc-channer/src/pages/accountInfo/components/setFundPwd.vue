<template>
 <el-dialog :title='isEdit===2?"修改资金密码":"设置资金密码"'  width='400px' :visible.sync='widgetVisible' :close-on-click-modal=' false ' class='dialog' :center='true' >
  <div>
      <el-form >
        <!-- <el-form-item    v-show="isEdit===2">
          <el-input
            size="small"
            autocomplete="off"
            type="password"
            style="width:100%"
            v-model="oldFundPwd"
            placeholder="请输入原密码" 
          ></el-input>
        </el-form-item> -->
        <el-form-item  >
          <el-input
            size="small"
            autocomplete="off"
            type="password"
            style="width:100%"
            v-model="newFundPwd"
            placeholder="请输入新密码" 
          ></el-input>
        </el-form-item>
        <el-form-item  >
          <el-input
            size="small"
            autocomplete="off"
            type="password"
            style="width:100%"
            v-model="newFundPwd2"
            placeholder="请确认新密码" 
          ></el-input>
        </el-form-item>
        <el-form-item  >
          <el-input
            size="small"
            autocomplete="off" 
            style="width:100%"
            v-model="code"
            placeholder="请输入手机验证码" 
          >
          <el-button :disabled="time>0" slot="append" type='primary'  @click="getCode()">{{ time>0?`${time}S后重新获取`:'获取验证码' }}</el-button>
          </el-input>
        </el-form-item>
      </el-form>
  </div>
  <div slot='footer' class='dialog-footer'>
   <el-button type=''  size='small' @click='widgetVisible=false'>取消</el-button>
   <el-button type='primary'  size='small' @click='submit()'>提交</el-button>
  </div>
 </el-dialog>
</template>
<script>
import { mapMutations } from "vuex";
export default {
 data() { 
  return {
   widgetVisible:false,
   isEdit:1,//1:第一次设置密码 2:修改资金密码
   oldFundPwd:'',//原资金密码（操作类型2需要传）
    newFundPwd:'',//新资金密码
    newFundPwd2:'',//新资金密码
    code:'',
    account:'',
    time:0,
    timer:null,
  }
 },
 mounted() {
 },
 methods: {
    ...mapMutations(["userMenus", "SetUserSet"]),
    show(isEdit,account){ 
        this.account = account
        this.isEdit = isEdit
        this.oldFundPwd=''
        this.newFundPwd='' 
        this.newFundPwd2='' 
        this.code=''
        this.widgetVisible = true  
    },
    submit(){
        
        // if (this.isEdit===2 && !this.oldFundPwd) {
        //     this.$message('请输入原密码')
        //     return;
        // }
        if(!this.newFundPwd){
            this.$message('请输入新密码')
            return;
        }
        if(!this.newFundPwd2){
            this.$message('请确认新密码')
            return;
        }
        if(this.newFundPwd!=this.newFundPwd2){
            this.$message('新密码两次输入不一致')
            return;
        }
        if(!this.code){
            this.$message('请输入邮箱验证码')
            return;
        }
        let form = {
            optType:this.isEdit,
            newFundPwd:this.newFundPwd,
            code:this.code
        }
        // if (this.isEdit===2){
        //     form={
        //         optType:this.isEdit,
        //         newFundPwd:this.newFundPwd,
        //         oldFundPwd:this.oldFundPwd,
        //         code:this.code
        //     }
        // }
        this.$http.post('/channel/user/setFundPwd',form).then((res) => {
            this.$message.success('设置成功')
            this.getUserSet() 
            if(this.$parent.getList){
                this.$parent.getList()
            }
            this.widgetVisible = false
        }).catch((err) => { 
         console.log(err)
        })
    },
    getUserSet() {
      this.$http.get('/channel/user/userInfo', {}).then((res) => {
        this.SetUserSet(res);
      });
    },
    getCode(){
        this.$http.post('/channel/common/sendSmsCode',{account:this.account,channel:'OTC',code_type:this.isEdit===1?'setf':'resetf'}).then((res) => {
         this.$message.success('验证码发送成功')
         this.time = 60
         this.setTime()
        }).catch((err) => { 
         console.log(err)
        })
    },
    setTime(){
        clearTimeout(this.timer)
        if(this.time>0){
            this.time --
            this.timer = setTimeout(()=>{
                this.setTime()
            },1000)
        }
    }
 },
 components: {
 }
}
</script>
<style lang='less' scoped>
.dialog{
}
</style>