<template>
  <el-dialog
    title="修改昵称"
    width="360px"
    :visible.sync="widgetVisible"
    :close-on-click-modal=" false "
    class="dialog"
    :center="true"
  >
    <div>
      <el-input
        size="small"
        autocomplete="off"
        style="width:100%"
        v-model="nickname"
        placeholder="  新昵称" 
      ></el-input>
    </div>
    <div slot="footer" class="dialog-footer">
      <el-button type size="small" @click="widgetVisible=false">取消</el-button>
      <el-button type="primary" size="small" @click="submit">提交</el-button>
    </div>
  </el-dialog>
</template>
<script>
export default {
  data() {
    return {
      widgetVisible: false,
      nickname: "",
    };
  },
  mounted() {},
  methods: {
    show() {
        this.nickname = ''
        this.widgetVisible = true;
    },
    submit() {
      if (!this.nickname) {
        this.$message("请输入新昵称");
        return;
      }
      this.$http
        .post("/channel/user/editNickname", { nickname: this.nickname })
        .then((res) => {
          this.$message.success("修改成功");
          this.widgetVisible = false;
          this.$parent.getList();
        })
        .catch((err) => {
          console.log(err);
        });
    },
  },
  components: {},
};
</script>
<style lang='less' scoped>
.dialog {
}
</style>