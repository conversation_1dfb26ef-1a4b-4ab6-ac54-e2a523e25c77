<template>
 <el-dialog title=' 修改登录密码'  width='400px' :visible.sync='widgetVisible' :close-on-click-modal=' false ' class='dialog' :center='true' >
  <div>
      <el-form >
        <el-form-item label="原密码:" label-width="130px">
          <el-input
            size="small"
            autocomplete="off"
            type="password"
            style="width:100%"
            v-model="oldpwd"
            placeholder="原密码" 
          ></el-input>
        </el-form-item>
        <el-form-item label="新密码" label-width="130px">
          <el-input
            size="small"
            autocomplete="off"
            type="password"
            style="width:100%"
            v-model="newlpwd"
            placeholder="新密码" 
          ></el-input>
        </el-form-item>
        <el-form-item label="确认密码:" label-width="130px">
          <el-input
            size="small"
            autocomplete="off"
            type="password"
            style="width:100%"
            v-model="newlpwd2"
            placeholder="确认密码" 
          ></el-input>
        </el-form-item>
      </el-form>
  </div>
  <div slot='footer' class='dialog-footer'>
   <el-button type=''  size='small' @click='widgetVisible=false'>取消</el-button>
   <el-button type='primary'  size='small' @click='submit()'>提交</el-button>
  </div>
 </el-dialog>
</template>
<script>
export default {
 data() { 
  return {
   widgetVisible:false,
   isEdit:1,//1:第一次设置密码 2:修改资金密码
   oldpwd:'',//原资金密码（操作类型2需要传）
    newlpwd:'',//新资金密码
    newlpwd2:'',//新资金密码
   
  }
 },
 mounted() {
 },
 methods: {
    show(isEdit){ 
        this.isEdit = isEdit
        this.oldpwd=''
        this.newlpwd='' 
        this.newlpwd2='' 
        this.widgetVisible = true 
    },
    submit(){
        
        if ( !this.oldpwd) {
            this.$message('请输入原密码')
            return;
        }
        if(!this.newlpwd){
            this.$message('请输入新密码')
            return;
        }
        if(!this.newlpwd2){
            this.$message('请确认新密码')
            return;
        }
        if(this.newlpwd!=this.newlpwd2){
            this.$message('新密码两次输入不一致')
            return;
        }
        this.$http.post('/channel/user/modifylpwd',{newlpwd:this.newlpwd, oldlpwd:this.oldpwd}).then((res) => {
        this.$message.success('修改成功')
         this.$parent.getList()
         this.widgetVisible = false
        }).catch((err) => { 
         console.log(err)
        })
    }
 },
 components: {
 }
}
</script>
<style lang='less' scoped>
.dialog{
}
</style>