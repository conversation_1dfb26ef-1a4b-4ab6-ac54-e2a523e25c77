<template>
 <el-dialog title='修改联系方式'  width='360px' :visible.sync='widgetVisible' :close-on-click-modal=' false ' class='dialog' :center='true' >
  <div>
       <el-input
            size="small"
            autocomplete="off"
            style="width:100%"
            v-model="mobile"
            placeholder="  新联系方式" 
          ></el-input>
  </div>
  <div slot='footer' class='dialog-footer'>
   <el-button type=''  size='small' @click='widgetVisible=false'>取消</el-button>
   <el-button type='primary'  size='small' @click='submit'>提交</el-button>
  </div>
 </el-dialog>
</template>
<script>
export default {
 data() { 
  return {
   widgetVisible:false,
   mobile:''
  }
 },
 mounted() {
 },
 methods: {
  show(){ 
      this.mobile = ''
      this.widgetVisible = true 
    },
  submit(){
      if(!this.mobile){
          this.$message('请输入新的联系方式')
          return
      }
      this.$http.post('/channel/user/editMobile',{mobile:this.mobile}).then((res) => {
       this.$message.success('修改成功')
       this.widgetVisible = false
       this.$parent.getList()
      }).catch((err) => { 
       console.log(err)
      })
  },
 },
 components: {
 }
}
</script>
<style lang='less' scoped>
.dialog{
}
</style>