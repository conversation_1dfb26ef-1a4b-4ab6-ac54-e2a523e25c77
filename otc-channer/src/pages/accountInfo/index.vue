<template>
  <div class="accountInfo">
    <h4 class="header">账户与安全</h4>
    <div class="form-data">
      <div class="form-line">
        <span class="label-name">昵称：</span> <div>{{tableData.nickname}}</div> 
      </div>
      <div class="form-line">
        <span class="label-name">账号：</span> <div>{{tableData.account}}</div> 
      </div>
      <div class="form-line">
        <span class="label-name">登录密码：</span> <div>{{tableData.login_pwd_seted == 111 ? '********' : '未设置'}}</div> <span class="edit" @click="setPwd()">修改</span>
      </div>
      <div class="form-line">
        <span class="label-name">资金密码：</span> <div>{{tableData.money_pwd_seted == 111 ? '********' : '未设置'}}</div> <span class="edit" @click="setFund()">{{tableData.money_pwd_seted == 111 ? '修改' : '设置'}}</span>
      </div>
      
      
    </div>

    
    <SetFundPwd ref="SetFundPwd"></SetFundPwd>
    <SetName ref="SetName"></SetName>
    <SetPwd ref="SetPwd"></SetPwd>
    <SetPhone ref="SetPhone"></SetPhone>
    
    <!-- <set-pwd ref="pwd" />
    <with-draw ref="draw" :createOrderInfo="createOrderInfo" />
    <crBB ref="bb" :createBBInfo="createBBInfo" /> -->

  </div>
</template>

<script>
import { mapState } from 'vuex';
import SetFundPwd from './components/setFundPwd'
import SetName from './components/setName'
import SetPwd from './components/setPwd'
import SetPhone from './components/setPhone'
export default {
    name:'accountInfo',
  components:{
    // SetPwd, WithDraw, crBB
  }, 
  data: function() {
    return {
        pageName:'accountInfo',
        tableData: {},
        dialogForm: false
    };
  },
  mounted() {
    this.getList();
  },
  computed: {
    ...mapState([''])
  },
  methods: {
    USDTAmountFormat(row, column,cellValue, index){
      return this.$tool.Fslice(cellValue) 
    },
    setPhone(){
        this.$refs.SetPhone.show()
    },
    setFund(){
        this.$refs.SetFundPwd.show(this.tableData.money_pwd_seted == 111 ?2:1,this.tableData.account) ////1:第一次设置密码 2:修改资金密码
    },
    setPwd(){
        this.$refs.SetPwd.show() ////1:第一次设置密码 2:修改资金密码
    },
    setName(){
        this.$refs.SetName.show(this.tableData.nickname)
    },
    RMBAmountFormat(row, column,cellValue, index){
      return this.$tool.Tslice(cellValue) 
    },
    getList(){
      this.$http.get('/channel/user/userInfo', {}).then(res=>{
        this.tableData = res;
      })
    },
    

    
  },
  components:{
      SetFundPwd,
      SetName,
      SetPwd,
      SetPhone
  }
};
</script>

<style scoped lang="less">
.accountInfo{
  .header{
    font-size: 16px;
    margin-bottom: 16px;
    margin-top: 28px; padding-left: 24px;
  }
  .form-data{
    padding: 30px;
    .form-line{
      height: 48px; line-height: 48px;
      border-bottom: 1px dotted #eeeeee;
      display: flex; align-items: center;
      justify-content: space-between;
      >div{
        flex: 1;
      }
      .label-name{
        width: 6em;
        text-align-last:justify;
        text-align:justify;
        margin-right: 16px;
      }
      .edit{
        color: #409EFF;
        cursor: pointer;
        width: 4em;
      }
    }
  }

}

</style>