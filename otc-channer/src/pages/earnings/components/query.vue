<template>
    <el-collapse value="filter">
		<el-collapse-item title="查询条件" name="filter">
            <el-form :inline="true"  ref="filterForm" :model="filterForm" size="mini">
                <el-form-item label="添加时间:">
                    <el-date-picker
                    @change=" getData "
                    v-model="date"
                    type="daterange"
                    align="left"
                    unlink-panels
                    range-separator="至"
                    start-placeholder="开始日期"
                    end-placeholder="结束日期"
                    value-format="yyyy-MM-dd"
                    >
                    </el-date-picker>
                </el-form-item>
                <!-- <el-form-item label="订单类型:">
                    <el-select @change=" getData " v-model="filterForm.order_type"  >
                        <el-option
                            v-for="item in orderTypeList"
                            :key="item.value"
                            :label="item.label"
                            :value="item.value">
                        </el-option>
                    </el-select>
                </el-form-item> -->
                <el-form-item label="订单号:">
                    <el-input v-model="filterForm.order_id" placeholder="订单号"></el-input>
                </el-form-item>
               
                <el-form-item label="昵称:">
                    <el-input v-model="filterForm.name" placeholder="昵称"></el-input>
                </el-form-item>
                <el-form-item>
                    <el-button  type="primary" @click.native="getData">查询</el-button>
                    <el-button  type="primary" @click="clear"  >清空</el-button>
                    <el-button  type="primary" @click="exportExcel"  icon="el-icon-document" >导出</el-button>
                </el-form-item>
            </el-form>
        </el-collapse-item>
    </el-collapse>
</template>
<script>
export default {
    data() { 
        return {
            date:[],
            filterForm:{
                start_time: '',
                end_time:'',
                order_id: '',
                order_type: '',
                name:'',
                page: 1,
                pagesize: 30,
            },
            orderTypeList:[
                {value:'',label:'全部'}, 
                {value:0,label:'购买'},
                {value:1,label:'提现'},  
            ],
            access:{
                adminInExcel: "N",
            }, 
        }
    },
    mounted() { 
        this.getData()
    },
    methods: {
        clear(){
            this.filterForm = {
                start_time: '',
                end_time:'',
                order_id: '',
                order_type: '',
                status:'',
                address:'',
                page: 1,
                pagesize: 30,
            }
            this.$emit('getData',this.filterForm)
        },
        getData(){
            this.filterForm.page = 1
            this.setDate()
            this.$emit('getData',this.filterForm)
        },
        exportExcel(){
            this.setDate()
            this.$emit('exportExcel',this.filterForm)
        },
        setDate(){
            if(this.date&&this.date.length>0){
                this.filterForm.start_time = this.date[0]+' 00:00:00'
                this.filterForm.end_time = this.date[1]+' 23:59:59'
            }else{
                this.filterForm.start_time = ''
                this.filterForm.end_time = ''
            }
        }
    },
    watch: {
    },

    components: {
    }
}
</script>
<style lang='less' scoped>
.query{
}
</style>