<template>
    <el-table
      :data="list"
      border
      size="mini"
      height="auto"
      stripe
    >
        <el-table-column type="index" width="50" align="center"></el-table-column> 
        <el-table-column prop="source_userid" label="用户编码"   align="center"></el-table-column>  
        <el-table-column prop="source_nick" label="昵称"   align="center"></el-table-column>  
        <el-table-column prop="currency" label="币种"  align="center"></el-table-column>
        <el-table-column prop="money_count" label="订单金额"  width="250" align="center">
            <div slot-scope="scope" >
                ¥{{ $tool.BigJs(scope.row.money_count,1,3,2) }}
            </div>
        </el-table-column>
        <el-table-column prop="dea_price" label="价格"   align="center">
            <div slot-scope="scope" >
                {{ $tool.BigJs(scope.row.dea_price,1,3,2) }}
            </div>
        </el-table-column>
        <el-table-column prop="amount" label="数量"  width="250" align="center">
            <div slot-scope="scope" >
                {{ $tool.BigJs(scope.row.amount,1,3,4) }}
            </div>
        </el-table-column> 
        <el-table-column prop="commission_rate" label="收益比例"  align="center">
            <div slot-scope="scope" >
                {{ $tool.BigJs(scope.row.commission_rate,100,3,2) }}%
            </div>
        </el-table-column>
        <el-table-column prop="commission_count" label="收益数量"  width="250" align="center">
            <div slot-scope="scope" >
                ¥{{ $tool.BigJs(scope.row.commission_count_cny,1,3,2) }} ≈ {{ $tool.BigJs(scope.row.commission_count,1,3,4) }} 
            </div></el-table-column> 
        <el-table-column prop="createdate" label="创建时间"  width="150" align="center"></el-table-column> 
        <el-table-column prop="order_id" label="订单号"  align="center"></el-table-column>
        
    </el-table>
</template>
<script>
export default {
  props: {
    list: {
      type: Array,
      default: [],
    },
    tableLoading: {
      type: Boolean,
      default: false,
    },
  },
  data() {
    return {};
  },
  mounted() {},
  methods: {
    btnClick(row, item) {
      console.log(row);
      console.log(item.type);
    }
  },
  components: {},
};
</script>
<style lang='less' scoped>
.table {
}
</style>