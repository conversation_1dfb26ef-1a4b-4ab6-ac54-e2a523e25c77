
<template>
  <div class="loginBody">
    <div class="Land">
      <div class="topzi">渠道管理系统</div>
      <div class="login-box" >
          <el-form :model="ruleForm" status-icon :rules="rules2" ref="ruleForm"  class="demo-ruleForm" :clearable="false">
              <el-form-item label="" prop="username">
                  <el-input  v-model="ruleForm.username" autocomplete="off"  placeholder="请输入账号"></el-input>
              </el-form-item>
              <el-form-item label="" prop="password">
                  <el-input type="password" v-model="ruleForm.password" :clearable="false"
                  @keyup.enter.native="submitForm('ruleForm')" placeholder="请输入密码"></el-input>
              </el-form-item>
              <!-- <el-form-item label="" prop="password" class="srcImg">
                  <el-input v-model="ruleForm.vercode" :clearable="false"
                  @keyup.enter.native="submitForm('ruleForm')" placeholder="请输入图片验证码"></el-input>
                  <img :src="servePath" @click="getImg" alt="图片">
              </el-form-item> -->
              <el-form-item>
                  <el-button class="loginBtn" type="success" @click="submitForm('ruleForm')">登录</el-button>
              </el-form-item>
          </el-form>
          <span class="setPwd" @click="setPwd">忘记密码</span>
      </div>
    </div>
    <SetPwd ref="SetPwd"></SetPwd>
  </div>
  
</template>

<script>
import SetPwd from './components/setPwd'
  export default {
    data() {
      var validateName = (rule, value, callback) => {
        if (value === '') {
          callback(new Error('请输入账号'));
        } else {
          callback();
        }
      };
      var validatePwd = (rule, value, callback) => {
        if (value === '') {
          callback(new Error('请输入密码'));
        } else {
          callback();
        }
      };
      var validateCode = (rule, value, callback) => {
        if (value === '') {
          callback(new Error('请输入验证码'));
        } else {
          callback();
        }
      };
      return {
        servePath: '',
        ruleForm: {
          username: '',
          // username: '',
          password: '',
          vercode: '',
        },
        rules2: {
          username: [
            { validator: validateName, trigger: 'blur' }
          ],
          password: [
            { validator: validatePwd, trigger: 'blur' }
          ],
          vercode: [
            { validator: validateCode, trigger: 'blur' }
          ]
        }
      };
    },
    created(){
      this.getImg()
    },
    methods: {
        setPwd(){
            this.$refs.SetPwd.show()
        },
      getImg(){
        let hash = parseInt(Math.random()*1000000)
        this.$nextTick(()=>{
           this.servePath = `${window.SERVER_PATH}${this.$tool.api.verify}/channel/login/verify?${hash}`
        })
      },
      submitForm(formName) {
        this.$refs[formName].validate((valid) => {
          if (valid) {
            let loginData={...this.ruleForm, password: (this.ruleForm.password)}
            this.$http.get('/channel/login/index', loginData).then(res=>{
              if(res.ischeck == 1) {
                window.location.href = res.url
                return
              }
                const {   userInfo,access_data } = res;
                localStorage.setItem('xpay_userInfo',JSON.stringify(userInfo));
                // let access =access_data
                // let ids = []
                // for (const key in access) { 
                //     ids.push(access[key].id)
                //     if(access[key].children && access[key].children.length>0){
                //         let children = access[key].children
                //         children.forEach(element => {
                //             ids.push(element.id)
                //             if(element.children && element.children.length>0){
                //                 let list = element.children
                //                 list.forEach(item => {
                //                     ids.push(item.id)
                //                 });
                //             }
                //         });
                //     }
                // }
                // console.log(access_data)
                // console.log(ids)
                // localStorage.setItem('ids',JSON.stringify(ids));
                let _this=this;
                _this.$router.replace('/home');
                
            })
          } else {
            return false;
          }
        });
      },
      resetForm(formName) {
        this.$refs[formName].resetFields();
      }
    },
    components:{
        SetPwd
    }
  }
</script>

<style lang="less" scoped>
.loginBody{
  width: 100%;
  height: 100%;
  background: rgb(233, 233, 233);
}
.setPwd{
    color:red;
    cursor: pointer;
}
.Land{
  width: 388px;
  // height: 305px;
  position: absolute;
  left: 50%;
  top: 50%;
  transform: translate(-50%,-50%);
  background: rgba(75, 74, 74, 0.4);
  padding: 25px 30px;
  border-radius: 6px;
  .topzi{
    text-align: center;
    font-size: 22px;
    color: #fff;
    font-weight: bold;
    margin-top: .5rem;
    margin-bottom: 1rem;
  }
  .loginBtn{
    width: 100%;
    font-size: 18px;
  }
}
</style>
