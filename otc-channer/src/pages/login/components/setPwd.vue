<template>
  <el-dialog
    title="重置登录密码"
    width="440px"
    :visible.sync="widgetVisible"
    :close-on-click-modal=" false "
    class="dialog"
    :center="true"
  >
    <div>
      <el-form v-if="state === 1" inline>
        <el-form-item label="手机号:">
          <el-input
            size="small"
            autocomplete="off"
            style="width:100%"
            v-model="account"
            placeholder="请输入手机号"
          >
          <el-button
              :disabled="time>0"
              slot="append"
              type="primary"
              @click="getCode()"
            >{{ time>0?`${time}S后重新获取`:'获取验证码' }}</el-button>
            </el-input>
        </el-form-item>

        <el-form-item label="验证码:">
          <el-input
            size="small"
            autocomplete="off"
            style="width:100%"
            v-model="code"
            placeholder="请输入验证码" 
            
          >
            
          </el-input>
        </el-form-item>
      </el-form>
      <el-form v-else>
        <el-form-item>
          <el-input
            size="small"
            autocomplete="off"
            type="password"
            style="width:100%"
            v-model="password"
            placeholder="请输入新密码" 
          ></el-input>
        </el-form-item>
        <el-form-item>
          <el-input
            size="small"
            autocomplete="off"
            type="password"
            style="width:100%"
            v-model="password2"
            placeholder="请确认新密码" 
          ></el-input>
        </el-form-item>
        
      </el-form>
    </div>
    <div slot="footer" class="dialog-footer">
        
      <el-button type size="small" @click="widgetVisible=false">取消</el-button>
      <el-button v-if="state === 1" type="primary" size="small" @click="verifyCode()">下一步</el-button>
      <el-button v-else type="primary" size="small" @click="submit()">提交</el-button>
    </div>
  </el-dialog>
</template>
<script>
export default {
  data() {
    return {
      widgetVisible: false,
      password: "", //新资金密码
      password2: "", //新资金密码
      code: "",
      account: "",
      time: 0,
      timer: null,
      state:1
    };
  },
  mounted() {},
  methods: {
    show() {
        this.state = 1
      this.password = "";
      this.password2 = "";
      this.code = "";
      this.account = "";
      this.widgetVisible = true;
    },
    submit() {
      if (!this.password) {
        this.$message("请输入新密码");
        return;
      }
      if (!this.password2) {
        this.$message("请确认新密码");
        return;
      }
      if (this.password != this.password2) {
        this.$message("新密码两次输入不一致");
        return;
      }
      let form = {
        password: this.password,
        account: this.account,
      };

      this.$http
        .post("/channel/login/subReset", form)
        .then((res) => {
          this.$message.success('密码重置成功')
          this.widgetVisible = false;
        })
        .catch((err) => {
          console.log(err);
        });
    },
    getCode() {
      if (!this.account) {
        this.$message("请输入手机号");
        return;
      }
      this.$http
        .post("/channel/common/sendSmsCode", {
          account: this.account,
          channel: "OTC",
          code_type: "resetl",
        })
        .then((res) => {
          this.$message.success("验证码发送成功，若长时间未收到请查看垃圾箱");
          this.time = 60;
          this.setTime();
        })
        .catch((err) => {
          console.log(err);
        });
    },
    verifyCode(){
        if (!this.account) {
            this.$message("请输入手机号");
            return;
        }
        if (!this.code) {
            this.$message("请输入验证码");
            return;
        }
        this.$http.post('/channel/login/resetCheckCode',{account:this.account,code:this.code}).then((res) => {
            this.state = 2
        }).catch((err) => { 
         console.log(err)
        })
    },
    setTime() {
      clearTimeout(this.timer);
      if (this.time > 0) {
        this.time--;
        this.timer = setTimeout(() => {
          this.setTime();
        }, 1000);
      }
    },
  },
  components: {},
};
</script>
<style lang='less' scoped>
.dialog {
}
/deep/.el-form-item__content{
    width: 320px;
}
</style>