<template>
    <div class="box">
        <el-tabs v-model="activeName"  @tab-click="handleClick">
            <el-tab-pane :label="item.label" :name="item.name" v-for=" (item,index) in paytypeList " :key="index"></el-tab-pane>  
        </el-tabs>

        <el-table
        :data="list"
        border
        size="mini"
        height="auto"
        stripe >
            <el-table-column type="index" width="50" align="center"></el-table-column> 
            <el-table-column prop="id" label="平台单号"   align="center"></el-table-column>
            <el-table-column prop="order_type" label="类型"   align="center"></el-table-column>
            <el-table-column prop="coin_type" label="币种"   align="center"></el-table-column>
            <el-table-column prop="money" label="订单金额 (CNY)" width="200"   align="center">
                    <div slot-scope="scope" > {{ $tool.BigJs(scope.row.money,1,3,2) }}  </div>
            </el-table-column>
            <el-table-column prop="price" label="汇率"   align="center">
                <div slot-scope="scope" >
                    {{ $tool.BigJs(scope.row.price,1,3,2) }}
                </div>
            </el-table-column>
            <el-table-column prop="amount" label="交易数量(USDT)" width="180"  align="center">
                <div slot-scope="scope" >
                    {{ $tool.BigJs(scope.row.amount,1,3,4) }}
                </div></el-table-column> 
            <el-table-column prop="sellteamname" label="收款团队名称"  width="100"  align="center"></el-table-column>
            <el-table-column prop="selluserid" label="收款方编号"  width="100"  align="center"></el-table-column>
            <el-table-column prop="payee_account" label="收款账号" width="160"  align="center"></el-table-column>
            <!-- <el-table-column prop="pay_type_name" label="收款账号类型" width="100"  align="center"></el-table-column> -->
            
            <el-table-column prop="sms_content" label="收款监控"   align="center" show-overflow-tooltip></el-table-column>
            <el-table-column prop="status_name" label="状态"   align="center"></el-table-column>
            <el-table-column prop="buy_usertype" label="付款方类型" width="100"  align="center"></el-table-column>
            <el-table-column prop="buyteamname" label="付款团队名称"  width="100"  align="center"></el-table-column>
            <el-table-column prop="buyuserid" label="付款方编号" width="100"  align="center"></el-table-column> 
            <el-table-column prop="merchant_order_id" label="商户订单号" width="180"  align="center"></el-table-column>
            
            <el-table-column prop="create_time" label="下单时间"  width="150" align="center"></el-table-column>
            <el-table-column prop="notify_amount_str" label="回调状态"   align="center"></el-table-column>
                     
            <el-table-column prop="proof_url" label="付款凭证"   align="center">
                 <div slot-scope="scope" > 
                   <span v-if="scope.row.proof_url" @click="showImages(scope.row.proof_url)" style="color:#66b1ff;cursor: pointer;">查看</span>
                   <span v-else>--</span>
                </div>
            </el-table-column>
            
            <el-table-column label="操作" width="200" align="center" fixed="right">
                <div slot-scope="scope" v-if="scope.row.op">
                <el-button
                    v-for=" (item,index) in scope.row.op "
                    :key="index"
                    type="text"
                    @click="btnClick(scope.row,item)"
                >{{ item.btn_name }}</el-button>
                </div>
            </el-table-column>
        </el-table>
    </div>
</template>
<script>

export default {
  props: {
    list: {
      type: Array,
      default: [],
    },
    tableLoading: {
      type: Boolean,
      default: false,
    },
    paytypeList:{
        type:Array,
        default:()=>[ ] 
    }
  },
  data() {
    return {
        activeName:'8201' ,
    };
  },
  mounted() {},
  methods: {
    btnClick(row, item) { 
        // console.log(item)
        if(item.type == 1){
            this.orderConfirm(row)
        }else if(item.type ==2 ){
            this.orderActivate(row)
        }else if(item.type ==3){
            this.noticeResend(row.id)
        }else if(item.type ==4){
            window.open(item.url)
        }
        
    },
    noticeResend(id){
        this.$http.post('/channel/fund/noticeResend',{id:id}).then((res) => {
         this.$message.success('补发成功')
         this.$parent.getData()
        }).catch((err) => { 
         console.log(err)
        })
    },
    showImages(url){
        console.log(url)
        this.$parent.payaway(url.split(','))
    },
    handleClick(){ 
        this.$parent.$refs.Query.getData({paytype:this.activeName})
    },
    orderActivate(row){ 
        this.$prompt('订单激活后状态回到已付款状态。', '确认激活吗？', {
            confirmButtonText: '确定',
            cancelButtonText: '取消',
            inputType:'password',
            inputPlaceholder:'请输入资金密码',
         inputValidator:(val)=>{
          if(val){
           return true
          }
          return false
         }
        }).then(({ value }) => {
            this.$http.post('/channel/fund/orderActivate',{id:row.id,fundPwd:value}).then((res) => {
                this.$message.success('激活成功')
                this.$parent.getData()
            }).catch((err) => { 
            console.log(err)
            })
        })
    },
    orderConfirm(row){
        this.$confirm('确认放行后系统将会把币直接转给买方,请与下级币商核实情况后再确认放行', '确认放行吗？',
            {confirmButtonText: '确定',cancelButtonText: '取消',}
        ).then(() => {
            this.$http.post('/channel/fund/orderConfirm',{orderID:row.id}).then((res) => {
                this.$message.success('成功放行')
                this.$parent.getData()
            }).catch((err) => { 
            console.log(err)
            })
        }).catch(() => {  
        });
    }
  },
  components: {},
};
</script>
<style lang='less' scoped>
.box {
    flex: 1;
    width: 100%;
    display: flex;
    flex-direction: column; 
    box-sizing: border-box;
    padding:  0 ;
    /deep/ .el-tabs__header {
            margin: 0;
        }
}
</style>