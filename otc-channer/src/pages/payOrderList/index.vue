<template>
    <div class='container'>
        <Query ref="Query" @getData = " getData " @exportExcel = " exportExcel "></Query>
        <Table ref="Table" :list=" list "  :paytypeList = "paytypeList"  ></Table>
        <div class="footer">
            <div class="count"> 
                <div>
                    <p>当前页金额：<span>{{ $tool.BigJs(pageMoneySum,1,3,2) }}</span> </p>
                    <p>总金额：<span>{{ $tool.BigJs(moneySum,1,3,2) }} </span></p>
                </div>
                <div>
                    <p>当前数量：<span>{{$tool.BigJs(pageAmountSum,1,3,4)}}</span></p>
                    <p>总数量：<span>{{$tool.BigJs(amountSum,1,3,4)}}</span></p>
                </div>
            </div> 
            <el-pagination
                @size-change="handleSizeChange"
                @current-change="handleCurrentChange"
                :current-page="filterForm.page"
                :page-size="filterForm.pagesize"
                :total="total*1"
                >
            </el-pagination>
        </div>
        <el-image-viewer v-if="showImgView" :url-list='images' :on-close="closeimg"></el-image-viewer>
    </div>
</template>
<script>

import  ElImageViewer  from 'element-ui/packages/image/src/image-viewer'
import Query from './components/query.vue'
import Table from './components/table.vue'
export default {
    data() { 
        return {
            filterForm:{
                page: 1,
                pagesize: 30,
            },
            list:[],
            total:0,
            paytypeList:[{name:'8201', label:'银行卡转卡'}],
            showImgView:false,
            images: [],
            pageMoneySum:'',
            pageAmountSum:'',
            moneySum:'',
            amountSum:''
        }
    },
    mounted() {
    },
    methods: {
        payaway( proof_url){
    //   this.images = ['https://cube.elemecdn.com/6/94/4d3ea53c084bad6931a56d5158a48jpeg.jpeg','https://fuss10.elemecdn.com/e/5d/4a731a90594a4af544c0c25941171jpeg.jpeg']
            this.images = proof_url
            this.showImgView = true
        },
        closeimg(){
            this.showImgView = false
        },
        getData(filterForm){
            if(filterForm){
                this.filterForm = filterForm
            }
            this.$http.post('/channel/fund/getPayOrderList',this.filterForm).then((res) => {
                this.list = res.data || []
                this.total = res.total || 0
                this.pageMoneySum=res.pageMoneySum    
                this.pageAmountSum=res.pageAmountSum    
                this.moneySum=res.moneySum    
                this.amountSum=res.amountSum  
                if(this.paytypeList.length===1){
                    for (const key in res.pay_type) {
                        if(key!=8201){
                            this.paytypeList.push({
                                name:key,
                                label:res.pay_type[key]
                            })
                        }
                        
                    }
                } 
                
            }).catch((res) => {
                console.log(res)
            })
        }, 
        exportExcel(filterForm) {
            if(filterForm){
                this.filterForm = filterForm
            }
            let p = this.$http.post('/channel/fund/getPayOrderExport',this.filterForm)
            p.then(res => {
                const list = res || []
                require.ensure([], () => {
                    const {
                        export_json_to_excel
                    } = require('../../assets/js/Export2Excel');
                    const tHeader = ['平台单号','类型','币种','订单金额(CNY)','汇率','交易数量(USDT)','收款团队名称','收款方编号','收款账号','收款监控','状态','付款方类型', '付款团队名称', '付款方编号','商户订单号','下单时间','回调状态']
      
                    const filterVal = ['id','order_type','coin_type','money','price','amount', 'sellteamname', 'selluserid','payee_account','sms_content','status_name','buy_usertype', 'buyteamname','buyuserid','merchant_order_id','create_time','notify_amount_str']
                    const data = this.formatJson(filterVal, list);
                    export_json_to_excel(tHeader, data, '交易记录');
                })
                }).catch(error => {
                return false
            })
        },
        formatJson(filterVal, jsonData) {
            return jsonData.map(v => filterVal.map(j => v[j]))
        },
        handleCurrentChange(val) {
			this.filterForm.page = val
			this.getData()
		},
		handleSizeChange(val) {
			this.filterForm.pagesize = val
			this.getData()
        },
    },
    components: {
        ElImageViewer,
        Query,
        Table
    }
}
</script>
<style lang='less' scoped>
.container{
    .count{
        display: flex;
        flex-direction: row;
        align-items: center;
        box-sizing: border-box;
        padding:  0 ;
        div{
            margin-right: 10px;
            span{
                color: #409EFF;
            }
        }
    }
}
</style>