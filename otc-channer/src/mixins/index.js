
export default {
    data() {
        return {
            env_pro_name: '',
        }
    },
    filters:{
       

    },
    computed:{
        
        
    },
    methods: {
        userBigJS(val1,val2,mt,to){
            let Big = require('big.js');
            let x = new Big(val1);
            let y = new Big(val2);
            switch(mt){
                case 1 : return x.plus(y).toString();break;
                case 2 : return x.minus(y).toString();break;
                case 3 : return x.times(y).toString();break;
                case 4 : return x.div(y).toString();break;
                default : val1
            }
        }
        
    }
}