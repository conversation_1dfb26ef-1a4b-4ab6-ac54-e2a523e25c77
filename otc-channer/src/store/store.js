import Vue from "vue";
import Vuex from "vuex";
Vue.use(Vuex);

const store = new Vuex.Store({
  state: {
    menus: [],
    userSet: {}
  },
  getters:{
    hasRouterPath: (state) =>{
      return state.menus
    }
  },
  mutations: {
    userMenus(state, obj){
      state.menus = new Array(...obj)
    },
    SetUserSet(state, obj){
      state.userSet = Object.assign({}, obj)
    }
  },
  actions:{
    // getMenuList({commit}, data){
    //   commit('userMenus', data)
    // }
  },
  modules:{

  }
});

export default store;
