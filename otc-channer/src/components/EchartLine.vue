<template>
    <div>
        <div ref="myEchart" :style="{width: '100%', height: '600px'}"></div>
    </div>
</template>

<script>
// import echarts from 'echarts';
    export default {
        name:'EchartLine',
        data(){
            return {
                chart:null,
                option:{
                    tooltip: {
                    trigger: 'axis', // 触发类型
                    axisPointer: {
                    // 指示器类型
                    type: 'line',
                    },
                    confine: true,
                    },
                    title: {
                        text: '',
                        top: 20,
                        x: '4%',
                    },
                     xAxis: {
                        type: 'category',
                        data: [],
                        boundaryGap: [0, 0.01],
                        axisTick: {
                        alignWithLabel: true //保证刻度线和label居中对齐
                        },
                    },
                    yAxis: {
                        type: 'value'
                    },
                    legend: {
                        data:["充值","提款","盈利"]
                    },
                    series: []
                }
            }
        },
        beforeDestroy() {
            if (!this.chart) {
            return
            }
            this.chart.dispose();
            this.chart = null;
        },
        methods:{
            getList(){
                this.$http.get(this.$tool.api.getChannelDaily,{}).then(res=>{
                            if(res.length>0){
                                // 时间
                                res.map(v=>{
                                    this.option.xAxis.data.push(v.optTime)
                                })
                                // 提款数据组装
                                    this.draw(res,'withdrawalAmount',"提款")
                                // 存款数据组装
                                    this.draw(res,'depositAmount',"充值")
                                // 盈利数据组装
                                    this.draw(res,'profitAmount',"盈利")
                                    this.initChart(this.option)
                            }
                        
                })
            },
            draw(list,key,name){
                        let data = [];
                        list.map(v=>{
                            data.push( Number(v[key] / 100 || 0).toFixed(2) )
                        })
                        let obj = {};
                            obj.data = data;
                            obj.name = name;
                            obj.type = "line";
                            obj.smooth = true;
                        this.option.series.push(obj)

            },

            initChart(option) {
                this.chart = echarts.init(this.$refs.myEchart);
                // 把配置和数据放这里
                this.chart.setOption(option)
                }
        },
        mounted(){
            this.getList();
        }
    }
</script>

<style scoped>

</style>