<template>
    <div class="totalList">
        <ul>
            <li v-for="(val,index) in list">
                {{val.label}}:&nbsp;{{val.value || 0}}
                <!-- <div></div>
                <div></div> -->
            </li>
        </ul>
    </div>
</template>

<script>
    export default {
        props:['list'],
        name:'totalList',
        data(){
            return {
                
            }
        },
        methods:{

        },
        mounted(){
            // console.log(this.list)
        }
    }
</script>

<style lang="less" scoped>
.totalList{
    width: 100%;
    padding: 14px 20px;
    color: #606266;
    font-size: 14px;
    ul{
        display: flex;
        width: 100%;
        li{
            // width: 20%;
            margin-right: 40px;
            text-align: center;
        }
    }
}
</style>