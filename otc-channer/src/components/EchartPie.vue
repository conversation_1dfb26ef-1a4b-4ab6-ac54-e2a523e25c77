<template>
    <div style="padding-left:50px;">
        <div ref="myEchart" :style="{width: '100%', height: '1000px'}"></div>
    </div>
</template>

<script>
// import echarts from 'echarts';
    export default {
        name:'EchartPie',
        data(){
            return {
                chart:null,
                option:{
                    title : {
                        text: '联赛投注分布',
                        subtext: '联赛 - 投注笔数',
                        x:'center'
                    },
                    tooltip : {
                        trigger: 'item',
                        formatter: "{a} <br/>{b} : {c} ({d}%)"
                    },
                    legend: {
                        orient: 'vertical',
                        left: 'left',
                        type: 'scroll',
                        data: []
                    },
                    series : [
                        {
                            name: '投注占比',
                            type: 'pie',
                            radius : '55%',
                            center: ['52%', '60%'],
                            data:[],
                            itemStyle: {
                                emphasis: {
                                    shadowBlur: 10,
                                    shadowOffsetX: 0,
                                    shadowColor: 'rgba(0, 0, 0, 0.5)'
                                }
                            }
                        }
                    ]
                }
            }
        },
        beforeDestroy() {
            if (!this.chart) {
            return
            }
            this.chart.dispose();
            this.chart = null;
        },
        methods:{
            getList(){
                this.$http.get(this.$tool.api.getBetRace,{}).then(res=>{
                        this.option.series[0].data = [];
                        this.option.legend.data = [];
                        if(res.length > 0){
                            res.map(v=>{
                            let obj = {};
                            obj.name = v.racename;
                            obj.value = Number(v.tybetcount) + Number(v.betcount);
                            this.option.series[0].data.push(obj);
                            this.option.legend.data.push(v.racename);
                        })
                            this.initChart(this.option)
                        }
                        
                        
                })
            },
            initChart(option) {
                this.chart = echarts.init(this.$refs.myEchart);
                // 把配置和数据放这里
                this.chart.setOption(option)
                }
        },
        mounted(){
            this.getList();
            // this.initChart(this.option)
        }
    }
</script>

<style scoped>

</style>