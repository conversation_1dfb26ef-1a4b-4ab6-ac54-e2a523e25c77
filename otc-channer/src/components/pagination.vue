
<template>
    <el-pagination
        background
        @size-change="handleSizeChange"
        @current-change="handleCurrentChange"
        :current-page="curPage"
        :page-size="pageSize"
        :page-sizes="pageSizes"
        :layout="layout"
        :total="total">
    </el-pagination>
</template>
<script>
export default {
    name:'pagination',
    data() {
      return {
        curPageSize: this.pageSize,
      };
    },
    props: {
        pageSize: {
            type: Number,
            default: 50
        },
        pageSizes: {
            type: Array,
            default: ()=>[10,50 ,100, 200,300]
        },
        curPage: {
            type: Number,
            default: 1
        },
        total: Number,
        layout:{
            type:String,
            default:'total, sizes, prev, pager, next, jumper'
        }
    },
    methods:{
        handleCurrentChange(currentVal) {
            this.$emit('handleChange', this.curPageSize, currentVal);
        },
        handleSizeChange(val) {
            this.curPageSize = val;
            this.$emit('handleChange', val, 1);
        },
    }
}
</script>
<style lang="less">
    .el-pagination{
        margin-top: 20px;
        text-align: center;
    }
    .el-pagination.is-background .el-pager li:not(.disabled).active{
        background-color: #55C0E7;
    }
</style>
