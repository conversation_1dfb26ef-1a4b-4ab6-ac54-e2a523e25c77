<template>
    <el-date-picker
      v-model="dateValue"
      type="datetimerange"
      align="right"
      unlink-panels
      range-separator="至"
      start-placeholder="开始日期"
      end-placeholder="结束日期"
      value-format="yyyy-MM-dd HH:mm:ss"
      @change="dataPickerChange"
      :default-time="defaultTime"
      size="mini"
    ></el-date-picker>
</template>
<script>
export default {
  name: "datePicker",
  model: {
    event: "change"
  },
  props: {
    value: [String, Array],
    defaultTime:{
      default:()=>{
        return ['00:00:00','23:59:59']
      },
      type:Array
      },
    dataList:{
      type:Array,
      default:()=>{
        return []
      }
    }
  },
  watch: {
    dataList(val){
      if(!val[0] || !val[1]){
        this.dateValue = [];
      }else{
        this.dateValue = this.dataList;
      }
    }
  },
  data() {
    return {
      dateValue:[],
      pickerOptions: {
        shortcuts: [
          // {
          //   text: "昨天",
          //   onClick(picker) {
          //     const end = new Date();
          //     const start = new Date();
          //     start.setTime(start.getTime() - 3600 * 1000 * 24 * 1);
          //     picker.$emit("pick", [start, end]);
          //   }
          // },
          // {
          //   text: "前天",
          //   onClick(picker) {
          //     const end = new Date();
          //     const start = new Date();
          //     start.setTime(start.getTime() - 3600 * 1000 * 24 * 2);
          //     picker.$emit("pick", [start, end]);
          //   }
          // },
          {
            text: "最近一周",
            onClick(picker) {
              const end = new Date();
              const start = new Date();
              start.setTime(start.getTime() - 3600 * 1000 * 24 * 7);
              picker.$emit("pick", [start, end]);
            }
          },
          {
            text: "最近一个月",
            onClick(picker) {
              const end = new Date();
              const start = new Date();
              start.setTime(start.getTime() - 3600 * 1000 * 24 * 30);
              picker.$emit("pick", [start, end]);
            }
          },
          {
            text: "最近三个月",
            onClick(picker) {
              const end = new Date();
              const start = new Date();
              start.setTime(start.getTime() - 3600 * 1000 * 24 * 90);
              picker.$emit("pick", [start, end]);
            }
          },
          {
            text: "最近半年",
            onClick(picker) {
              const end = new Date();
              const start = new Date();
              start.setTime(start.getTime() - 3600 * 1000 * 24 * 90 * 2);
              picker.$emit("pick", [start, end]);
            }
          },
          {
            text: "最近一年",
            onClick(picker) {
              const end = new Date();
              const start = new Date();
              start.setTime(start.getTime() - 3600 * 1000 * 24 * 90 * 4);
              picker.$emit("pick", [start, end]);
            }
          },
          {
            text: "全部",
            onClick(picker) {
              const end = "";
              const start = "";
              picker.$emit("pick", [start, end]);
            }
          }
        ]
      }
    };
  },
  methods: {
    dataPickerChange(v) {
      if(v){
        this.$emit('selectChange',v)
      }else{
        this.$emit('selectChange',["",""])
      }
    },
    reset() {
      this.dateValue = this.value;
    },

    },

  mounted(){
  }
};
</script>



