<template>
    <div class="online">
        <div>
            <i>{{count}}</i>
            <i>在线人数</i>
        </div>
    </div>
</template>

<script>
    export default {
        name:'online',
        data(){
            return {
                count:0,
                timer:null
            }
        },
        methods:{
            getData(){
                this.$http.get(this.$tool.api.getOnline,{},{hideLoading:true}).then(res=>{
                    this.count = res.online || 0;
                })
            },

        },
        destroyed(){
            if(this.timer){
                clearInterval(this.timer)
            }
        },
        mounted(){
            this.getData()
            this.timer = setInterval(()=>{
                this.getData()
            },180000)
        }
    }
</script>

<style lang="less" scoped>
.online{
    overflow: hidden;
    height: 100%;
    padding: 0 10px;
    cursor: pointer;
    >div{
        float: left;
        text-align: center;
        width: 90px;
        height: 80%;
        display: inline-block;
        overflow: hidden;
        padding-top: 6px;
        i{
            display: block;
            line-height: 24px;
            &:first-child{
                color: #409EFF;
            }
        }
    }
}
</style>