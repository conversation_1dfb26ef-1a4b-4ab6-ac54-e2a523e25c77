<template>
    <div class="websocketList">
            <audio :src="audioSrc" autoplay="autoplay"></audio>
        <div v-if="recharge" @click="$router.push({path:'/task/rechargeApply',query:{showAll:true}})">
            <i>{{productinfos.chargeNumber || 0}}</i>
            <i>存款待审核</i>
        </div>
        <div v-if="withdraw" @click="$router.push({path:'/task/withdrawApply',query:{showAll:true}})">
            <i>{{productinfos.withdrawNumber || 0}}</i>
            <i>提款待审核</i>
        </div>
        <div v-if="transaction" @click="$router.push({path:'/task/taskTransactionList'})">
            <i>{{productinfos.withdrawCommission || 0}}</i>
            <i>佣金结算审核</i>
        </div>
            <audio id="audio1" preload="auto" loop="loop">
                <!-- <source  src="../assets/1702.mp3" type="audio/ogg" /> -->
            </audio>
            <audio id="audio2" preload="auto" loop="loop">
                <!-- <source src="../assets/line.mp3" type="audio/ogg" /> -->
            </audio>
    </div>
</template>

<script>    
    export default {
    name:'initWebsocket',
    data(){
        return {
            productinfos:{},
            transaction:false,
            recharge:false,
            withdraw:false,
            keepObj:{},
            audioSrc:'',
            isPlaying:false,
            audioTimer:null,
            lastRunTime: Date.now(),//上次播放声音的时间
            websock: null,//建立的连接
            lockReconnect: false,//是否真正建立连接
            timeout: 28000,//30秒一次心跳
            timeoutObj: null,//心跳心跳倒计时
            serverTimeoutObj: null,//心跳倒计时
            timeoutnum: null,//断开 重连倒计时
            count:0,//重连次数超过10次，就不再重连
            connectOver:false,
            audioType:0,//声音类型
        }
    },
    props:{
        menuList:Array
    },
    computed:{
        // menuList(){
        //     return this.$store.state.menu.menuList
        // }
    },
    destroyed() {
        //页面销毁时关闭长连接
        clearTimeout(this.timeoutObj);
        clearTimeout(this.serverTimeoutObj);
        this.websocketclose();
    },
    methods:{
        aplayAudio(){
                // this.audioSrc = '../assets/1702.mp3';
                // let _this = this;
                // setTimeout(()=>{
                //     _this.audioSrc = ''
                // },3000)
               this.lastRunTime = Date.now()
                let audio = this.audioType == 1 ? document.querySelector('#audio1') : document.querySelector('#audio2')
                if (!this.isPlaying) {
                    audio.play()
                    this.isPlaying = true
                }
                this.audioTimer = setTimeout(() => {
                    this.stop()
                }, 10000)
        },
        stop () {
            this.currentTime = Date.now()
            let audio = this.audioType == 1 ? document.querySelector('#audio1') : document.querySelector('#audio2')
            if (this.currentTime - this.lastRunTime < 10000) {
            } else {
                if (this.isPlaying) {
                    audio.currentTime = 0
                    audio.pause()
                    this.isPlaying = false
                }
            }
            clearTimeout(this.audioTimer)
        },
        initWebSocket(firstLogin = ''){
            const wsuri = this.$modal.websockUrl + localStorage['xpay_userId'] + firstLogin;
            this.websock = new WebSocket(wsuri);
            this.websock.onopen = this.websocketopen;
            this.websock.onmessage = this.websocketonmessage;
            this.websock.onclose = this.websocketclose;
            this.websock.onerror = this.websocketerror;
        },
        reconnect() {//重新连接
            var that = this;
            this.count++;
            // console.log(this.count)
            if(this.count > 130){
                // 不再连接
                return;
            }
            if(that.lockReconnect) {
                return;
            };
            that.lockReconnect = true;
            //没连接上会一直重连，设置延迟避免请求过多
            that.timeoutnum && clearTimeout(that.timeoutnum);
            that.timeoutnum = setTimeout(function () {
                //新连接
                that.initWebSocket();
                that.lockReconnect = false;
            },5000);
        },
        reset(){//重置心跳
            var that = this;
            //清除时间
            clearTimeout(that.timeoutObj);
            clearTimeout(that.serverTimeoutObj);
            //重启心跳
            that.start();
        },
 
         start(){//开启心跳
            var self = this;
            self.timeoutObj && clearTimeout(self.timeoutObj);
            self.serverTimeoutObj && clearTimeout(self.serverTimeoutObj);
            self.timeoutObj = setTimeout(function(){
                //这里发送一个心跳，后端收到后，返回一个心跳消息，
                if (self.websock.readyState == 1) {//如果连接正常
                    self.websock.send('heartCheck');
                }else{//否则重连
                    self.reconnect();
                }
                self.serverTimeoutObj = setTimeout(function() {
                    //超时关闭
                    self.websock.close();
                }, self.timeout);

            }, self.timeout)
        },
        websocketopen(){//打开
            //开启心跳
            this.start();
        },
        websocketonmessage(e){ //数据接收
        // console.log(e)
        // if(e.data == 'success'){
            this.count = 0;
            this.reset();
        // }
        // if(e.data == 'failure'){
        //     let _this = this;
        //     //清除时间
        //     this.connectOver = true;
        //     this.websock.close();//防止多端登陆
        // }
        if(e.data.indexOf('transaction_audit_data')>-1 || e.data.indexOf('transaction_into_data')>-1){
            let obj = JSON.parse(e.data);
             if(obj.dateType == 'transaction_audit_data'){
                this.audioType = 2
             }
             if(obj.dateType == 'transaction_into_data'){
                this.audioType = 1
             }
                this.aplayAudio();
            if(obj.dateType == 'transaction_audit_data' || obj.dateType == 'transaction_into_data'){
                let res = obj.object;
                if(this.keepObj.chargeNumber < res.chargeNumber){
                    this.$notify({
                            title: "提示",
                            message: `你有一笔新的充值申请需要审核`,
                            type: "warning"
                        });
                }
                if(this.keepObj.withdrawNumber < res.withdrawNumber){
                    this.$notify({
                            title: "提示",
                            message: `你有一笔新的提现申请需要审核`,
                            type: "warning"
                        });
                }
                if(this.keepObj.withdrawCommission < res.withdrawCommission){
                    this.$notify({
                            title: "提示",
                            message: `你有一笔新的佣金结算需要审核`,
                            type: "warning"
                        });
                }
                this.productinfos = obj.object;
                this.keepObj = obj.object;
            }
        }
        },
        websocketclose(option){  //关闭
            // console.log(option)
            // if(this.connectOver){
                clearTimeout(this.timeoutObj);
                clearTimeout(this.serverTimeoutObj);
            //     return
            // }
            this.websock.close();//必须要关闭，否则未断开又服务器会抛异常
            if(localStorage['xpay_userId']){
                //重连
                this.reconnect();
            }
            
        },
        websocketerror(){  //失败
            // console.log('连接失败')
            // if(this.connectOver){
                clearTimeout(this.timeoutObj);
                clearTimeout(this.serverTimeoutObj);
            //     return
            // }
            this.websock.close();//必须要关闭，否则未断开又服务器会抛异常
            //重连
            if(localStorage['xpay_userId']){
                //重连
                this.reconnect();
            }
        },
        init(){
            this.$http.get(this.$tool.api.initCountAuditData,{}).then(res=>{
                this.productinfos = res;
                this.keepObj = res
            })
        }
    },
   
    watch:{
        // 权限遍历
        menuList(val){
            let menuList = val;
             for (let i = 0; i < menuList.length; i++) {
                menuList[i].childMenus.forEach(element => {
                    // 佣金提款审核 PqoTLHoWwH7WesUp
                    if(element.menuCode == 'PqoTLHoWwH7WesUp' || element.menuName == "佣金提款审核"){
                        this.transaction = true;
                    }
                    // 存款
                    if(element.menuCode == 'uIgxZPg0WwMFPxBZ' || element.menuName == "存款申请"){
                        this.recharge = true;
                    }
                    // 提款
                    if(element.menuCode == 'BljPyWWuOjhfKl3N' || element.menuName == "提款申请"){
                        this.withdraw = true;
                    }
                });
                
            }
        }   
    },
    mounted(){
        this.init();//初始化数据
        this.initWebSocket();//连接
         //刷新时弹出提示
         let _this = this;
        window.onbeforeunload = e => {     
            _this.websocketclose();//必须要关闭，否则未断开又服务器会抛异常
        };
        }
    }
</script>

<style scoped lang="less">
.websocketList{
    overflow: hidden;
    height: 100%;
    padding: 0 10px;
    cursor: pointer;
    >div{
        float: left;
        text-align: center;
        width: 90px;
        height: 80%;
        display: inline-block;
        overflow: hidden;
        padding-top: 6px;
        i{
            display: block;
            line-height: 24px;
            &:first-child{
                color: red;
            }
        }
    }
}

</style>