<template>
  <div style="margin-top:15px;">
    <el-button size="small" type="success" icon="el-icon-folder-opened" @click="exportExcel" :disabled="!isDisabled">导出数据</el-button>
  </div>
</template>

<script>
import FileSaver from 'file-saver'
// import XLSX from 'xlsx'
export default {
  name:'exportExcel',
  data(){
      return{
      }
  },
  props: {
        excelName:String,//表格名称
        isDisabled: {
          type: Boolean,
          default: true
        }
    },
  methods: {
    //导出的方法
     exportExcel () {
       let table = document.querySelector("#outTable").cloneNode(true);
        // 因为element-ui的表格的fixed属性导致多出一个table，会下载重复内容，这里删除掉
        if(table.querySelector(".el-table__fixed-right")){
          table.removeChild(table.querySelector(".el-table__fixed-right"))
        }
        // if(table.querySelector(".el-table__fixed-left")){
        //   table.removeChild(table.querySelector(".el-table__fixed-left"))
        // }
       //坑点:转换成excel时，使用原始的格式，没有这个参数会显示乱码
       let xlsxParam = { raw: true };
        //  每个el-table 的ID
         let wb = XLSX.utils.table_to_book(table,xlsxParam)
         let wbout = XLSX.write(wb, { bookType: 'xlsx', bookSST: true, type: 'array' })
         try {
             FileSaver.saveAs(
               new Blob([wbout], 
             { type: 'application/octet-stream;charset=utf-8'}), 
             `${this.excelName}.xlsx`
             )
         } catch (e) { 
           if (typeof console !== 'undefined') console.log(e, wbout) }
         return wbout
    },
  }
};
</script>

<style scoped>
</style>