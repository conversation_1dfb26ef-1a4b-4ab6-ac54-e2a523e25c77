<template>
    <div>
        <div ref="myEchart" :style="{width: '80%', height: '500px',margin:'0 auto'}"></div>
    </div>
</template>

<script>
// import echarts from 'echarts';
    export default {
        name:'EchartBar',
        data(){
            return {
                chart:null,
                option:{
                    color: ['#409EFF'],
                    title : {
                    text: '投注时间-投注量',
                    x:'center'
                },
                    tooltip : {
                        trigger: 'axis',
                        axisPointer : {            
                            type : 'shadow'        
                        }
                    },
                    grid: {
                        left: '3%',
                        right: '4%',
                        bottom: '3%',
                        containLabel: true
                    },
                    xAxis : [
                        {
                            type : 'category',
                            data : [],
                            axisTick: {
                                alignWithLabel: true
                            }
                        }
                    ],
                    yAxis : [
                        {
                            type : 'value'
                        }
                    ],
                    series : [
                        {
                            name:'投注量',
                            type:'bar',
                            barWidth: '60%',
                            data:[]
                        }
                    ]
                }
            }
        },
        beforeDestroy() {
            if (!this.chart) {
            return
            }
            this.chart.dispose();
            this.chart = null;
        },
        methods:{
            getList(){
                this.$http.get(this.$tool.api.getBetDaily,{}).then(res=>{
                        this.option.series[0].data = [];
                        this.option.xAxis[0].data = [];
                        if(res.length > 0){
                                res.map(v=>{
                                this.option.series[0].data.push( ((Number(v.bet)+Number(v.tybet)) ) );
                                this.option.xAxis[0].data.push(v.dateid);
                            })
                            this.initChart(this.option)
                        }
                        
                        
                })
            },
            initChart(option) {
                this.chart = echarts.init(this.$refs.myEchart);
                // 把配置和数据放这里
                this.chart.setOption(option)
                }
        },
        mounted(){
            this.getList();
            // this.initChart(this.option)
        }
    }
</script>

<style scoped>

</style>