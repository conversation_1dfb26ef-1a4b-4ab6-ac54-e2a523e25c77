import Vue from 'vue'
import App from './App.vue'

import router from './router'

import store from './store/store'
import elementComponents from './element';
import 'element-ui/lib/theme-chalk/index.css';
import Viewer from 'v-viewer'
import 'viewerjs/dist/viewer.css';
elementComponents.forEach(comp=>{
    Vue.use(comp)
})
import Controller from './common/controller';
import ElementUI from 'element-ui';
import components from './common/component';


import modal from './common/modal';
import Http from './common/http';
import mixin from './mixins'
import './common/global.js'   //自定义的常量
import './common/dict.js'   //过滤器
import VueClipboard from 'vue-clipboard2'
Vue.use(VueClipboard)
Vue.config.productionTip = false
Vue.config.devtools = true
Vue.use(ElementUI);
Vue.use(Http);
Vue.use(Controller);
Vue.use(components);
Vue.use(Viewer);
Vue.use(modal);

Vue.mixin(mixin)
new Vue({
  router,
  store,
  render: h => h(App)
}).$mount('#app')
