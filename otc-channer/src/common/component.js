import Vue from 'vue';
import Pagination from '@/components/pagination';
import EchartLine from '@/components/EchartLine';
import EchartPie from '@/components/EchartPie';
import EchartBar from '@/components/EchartBar';
import DatePicker from '@/components/datePicker';
import ExportExcel from '@/components/exportExcel';
import InitWebsocket from '@/components/initWebsocket';
import Online from '@/components/online';
import TotalList from '@/components/totalInfo';


const projectComponents = [
  Pagination,
  EchartLine,
  EchartPie,
  EchartBar,
  DatePicker,
  ExportExcel,
  InitWebsocket,
  Online,
  TotalList,
];

const plugin = {
  install(Vue) {
    projectComponents.forEach((comp) => {
      Vue.component(comp.name, comp);
    });
  },
};


export default plugin;
export const install = plugin.install;