
const arrayList = {
    orderStatus : [
        {value:'1',label:'未付款'},{value:'2',label:'超时取消'},
        {value:'3',label:'已付款'},{value:'4',label:'付款超时'},
        {value:'5',label:'冻结'},{value:'6',label:'超时完成'},
        {value:'7',label:'已完成'},{value:'8',label:'扣款失败取消'},
        {value:'9',label:'申诉中'},{value:'10',label:'回复申诉中'},
        {value:'11',label:'申诉完成'},{value:'12',label:'冻结失败'},
        {value:'13',label:'审核中'},{value:'14',label:'未审核'},
        {value:'15',label:'审核不通过'}
    ] ,

    payTypeList : [
        {value: '8101' ,label:'支付宝'},{value: 8201,label:'银行卡'},
        {value: 8301,label:'微信'}
    ]
    

};



let bindToGlobal = (obj,key='var')=>{
    if(typeof window[key] === 'undefined'){
        window[key] = {};
    }
    for(let i in  obj){
        window[key][i] = obj[i]
    }
};
bindToGlobal(arrayList,'_arrList');
