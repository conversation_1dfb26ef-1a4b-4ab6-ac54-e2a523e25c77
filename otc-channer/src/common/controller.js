 const tool = {
    //  请求api前面需添加不同标志，如api-user 转发到不同端口获取数据，所以统一处理
    api:{
        login: '/merchant/login/index',  //用户登录接口
        verify: '/merchant/login/verify',  //获取图形验证码
        logout: '/merchant/login/logout',  //退出登录
        getOrderList: '/merchant/buy/getOrderList',  //获取充值记录
        buygetDetail: '/merchant/buy/getDetail',  //查看订单详情
        buyExport: '/merchant/buy/export',   //充值记录导出
        sellgetOrderList: '/merchant/sell/getOrderList',  //获取提现记录
        getUserInfo: '/merchant/user/getUserInfo',  //获取用户信息
        sellCreate:'/merchant/sell/create',  //获取提现窗口数据
        subCreate: '/merchant/sell/subCreate',  //创建提现单
        sellOrderAudit: '/merchant/sell/sellOrderAudit',  //单个、批量审核提现单
        sellOrderPass: '/merchant/sell/sellOrderPass',  //提现单 确认放行
        userIndex: '/merchant/user/index',  //我的资产
        withdraw: '/merchant/fund/withdraw',  //获取提币窗口数据
        subWithdraw: '/merchant/fund/subWithdraw',  //创建提币单
        withdrawgetOrderList: '/merchant/fund/getOrderList',  //获取提币记录
        withdrawAudit: '/merchant/fund/withdrawAudit',  //单个、批量审核提币单
        getSubOrderList: '/merchant/sell/getSubOrderList',
        setFundPwd: '/merchant/user/setFundPwd',
        sellexport: '/merchant/sell/export',
        commonUpload: '/merchant/common/upload',
        indexHome: '/merchant/index/home',
        getFundList: '/merchant/fund/getFundList', 
        exportFundList: '/merchant/fund/exportFundList',
        userInfo: '/merchant/user/userInfo',
        accountStatus: '/merchant/user/accountStatus',
        

    }, 
    // 转化时间搓
    timer(timestamp,flag) {
        if(!timestamp){
            return ''
        }
        if(String(timestamp).length == 13){
            if(timestamp && timestamp != 'null'){
                let time = new Date(timestamp);
                let y = time.getFullYear();
                let m = time.getMonth()+1;
                let d = time.getDate();
                let h = time.getHours();
                let mm = time.getMinutes();
                let s = time.getSeconds();
                if(flag){
                    return y+'-'+add0(m)+'-'+add0(d)
                }else{
                    return y+'-'+add0(m)+'-'+add0(d)+' '+add0(h)+':'+add0(mm)+':'+add0(s);
                }
            }else{
                return '';
            }
            function add0(m){return m<10?'0'+m:m }
        }else if(String(timestamp).length == 10){
            if(timestamp && timestamp != 'null'){
                let time = new Date(timestamp*1000);
                let y = time.getFullYear();
                let m = time.getMonth()+1;
                let d = time.getDate();
                let h = time.getHours();
                let mm = time.getMinutes();
                let s = time.getSeconds();
                if(flag){
                    return y+'-'+add0(m)+'-'+add0(d)
                }else{
                    return y+'-'+add0(m)+'-'+add0(d)+' '+add0(h)+':'+add0(mm)+':'+add0(s);
                }
            }else{
                return '';
            }
            function add0(m){return m<10?'0'+m:m }
        }else {
            return timestamp
        }
    },
    //随机生成8位邀请码
    randomWord(min, max,randomFlag){
        var str = "",
            range = min,
            pos = null,
            arr = ['0', '1', '2', '3', '4', '5', '6', '7', '8', '9', 'a', 'b', 'c', 'd', 'e', 'f', 'g', 'h', 'i', 'j', 'k', 'l', 'm', 'n', 'o', 'p', 'q', 'r', 's', 't', 'u', 'v', 'w', 'x', 'y', 'z', 'A', 'B', 'C', 'D', 'E', 'F', 'G', 'H', 'I', 'J', 'K', 'L', 'M', 'N', 'O', 'P', 'Q', 'R', 'S', 'T', 'U', 'V', 'W', 'X', 'Y', 'Z'];
        // 随机产生
        if(randomFlag){
            range = Math.round(Math.random() * (max-min)) + min;
        }
        for(var i=0; i<range; i++){
            pos = Math.round(Math.random() * (arr.length-1));
            str += arr[pos];
        }
        return str;
    },
    // 字体颜色
    classCalc(v){
        if(v){
          return v > 0 ? 'green' : 'red'
        }else{
          return 'green'
        }
    },
    // 获取今天00:00:00-23:59:59时间
    getTodayTime(){
        let date1 = new Date(new Date(new Date().toLocaleDateString()).getTime());
        let date2 = new Date(new Date(new Date().toLocaleDateString()).getTime()+24*60*60*1000-1);
        let startTime = date1.getFullYear() + "-" + ((date1.getMonth() + 1) < 10 ? "0" + (date1.getMonth() + 1):(date1.getMonth() + 1))+ "-" + (date1.getDate() < 10 ? "0" + date1.getDate():date1.getDate()) + " " + (date1.getHours()<10?"0"+date1.getHours():date1.getHours()) + ":" + (date1.getMinutes()<10?"0"+date1.getMinutes():date1.getMinutes()) + ":" + (date1.getSeconds()<10?"0"+date1.getSeconds():date1.getSeconds())
        let endTime = date2.getFullYear() + '-' + (date2.getMonth() + 1) + '-' + date2.getDate() + ' ' + date2.getHours() + ':' + date2.getMinutes() + ':' + date2.getSeconds()
        return [new Date(startTime).getTime(),new Date(endTime).getTime()]
    },
    // 获取今天00:00:00-23:59:59时间 rep:2019-12-11 00:00:00
    getTodayDate(){
        let date1 = new Date(new Date(new Date().toLocaleDateString()).getTime());
        let date2 = new Date(new Date(new Date().toLocaleDateString()).getTime()+24*60*60*1000-1);
        let startTime = date1.getFullYear() + "-" + ((date1.getMonth() + 1) < 10 ? "0" + (date1.getMonth() + 1):(date1.getMonth() + 1))+ "-" + (date1.getDate() < 10 ? "0" + date1.getDate():date1.getDate()) + " " + (date1.getHours()<10?"0"+date1.getHours():date1.getHours()) + ":" + (date1.getMinutes()<10?"0"+date1.getMinutes():date1.getMinutes()) + ":" + (date1.getSeconds()<10?"0"+date1.getSeconds():date1.getSeconds())
        let endTime = date2.getFullYear() + '-' + ((date2.getMonth() + 1) < 10 ? "0" + (date2.getMonth() + 1):(date2.getMonth() + 1)) + '-' + date2.getDate() + ' ' + date2.getHours() + ':' + date2.getMinutes() + ':' + date2.getSeconds()
        return [startTime,endTime]
    },
    Fslice(val){
        if(!val) return '0.0000'
        let strArr = String(val).split('.')
        if(strArr[1]){
            return `${strArr[0]}.${strArr[1].slice(0,4)}`
        }else{
            return `${strArr[0]}.0000`
        }
    },
    Tslice(val){
        if(!val) return '0.00'
        let strArr = String(val).split('.')
        if(strArr[1]){
            return `${strArr[0]}.${strArr[1].slice(0,2)}`
        }else{
            return `${strArr[0]}.00`
        }
    },
    BigJs(val1,val2,mt,to) {
        const Big = require('big.js');
        var x = new Big(val1 || 0)
        var y = new Big(val2 || 0)
        try {
            if(to){
                switch(mt){
                    case 1 : return x.plus(y).toFixed(to).toString();break;
                    case 2 : return x.minus(y).toFixed(to).toString();break;
                    case 3 : return x.times(y).toFixed(to).toString();break;
                    case 4 : return x.div(y).toFixed(to).toString();break;
                    default : return x.toFixed(to).toString()
                }
            }
        } catch (e) {
            return '0.00'
        }
    },
    returnLabel(val,arr) {
        var length = arr.length;
        for (var k = 0 ; k  < length ; k++) {
            if(arr[k].value == val){
    
                return arr[k].label
            }
        }
        return val ? val : '无';
    },

}

export default {
    install(Vue) {
      Object.defineProperty(Vue.prototype, '$tool', {
        value: tool,
        writable: false,
      });
    },
};