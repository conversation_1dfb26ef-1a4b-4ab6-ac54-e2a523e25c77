import axios from 'axios';
import { MessageBox,Notification , Loading} from 'element-ui';
import router from '../router'
import Qs from "qs";
// import md5 from 'js-md5'
axios.defaults.baseURL = window.SERVER_PATH
axios.defaults.withCredentials=true
// (process.env.NODE_ENV === 'development' ? axios.defaults.baseURL= '' : axios.defaults.baseURL = window.SERVER_PATH)
let loading;       
function startLoading() {   
    loading = Loading.service({
        lock: true,
        text: '正在加载中……',
        background: 'rgba(0, 0, 0, 0.7)'
    })
}
function endLoading() {    
    loading.close()
}
let needLoadingRequestCount = 0;

export function showFullScreenLoading() {
    if (needLoadingRequestCount === 0) {
        startLoading()
    }
    needLoadingRequestCount++
}

export function tryHideFullScreenLoading() {
    if (needLoadingRequestCount <= 0) return
    needLoadingRequestCount--
    if (needLoadingRequestCount === 0) {
        endLoading()
    }
}

const httpInstance = axios.create({
  responseType: 'json',
  timeout: 30000,
  params: {},
  data: {},
  toQs:false,//是否需要序列化
  postType:1,//1需要一层body封装上传 2不需要
  headers: {},
  hideLoading:false,
  validateStatus(status) {
    return status >= 200 && status < 300;
  },
});

function responseErrorHandler(error) {
  let text = '未知错误';
  let response = JSON.parse(JSON.stringify(error));
  console.log(response)
  if (response.data) {
    const { code, msg } = response.data;
    text = msg ? msg : text; 
     if (code === -2) {
      localStorage.removeItem('xpay_token');
      router.replace('/login');
    }else if(response.config && !response.config.hideLoading){ 
        Notification.error(text) 
    }
  } else { 
    errorConfirm(text)  
  } 
}
function errorConfirm(){ 
    var text = '服务器响应失败';
    if(window.location.href.indexOf('/login') > -1){
        Notification.error(text) 
    } else{
        if(localStorage.getItem('confirmTime')*1+1000 < new Date().getTime() ){
            localStorage.setItem('confirmTime',new Date().getTime());
            MessageBox.confirm('建议刷新页面或重新登录账号', text,
                {confirmButtonText: '重新登录',cancelButtonText: '刷新页面',type:'error',distinguishCancelAndClose: true,} 
            ).then(() => { 
                localStorage.setItem('confirmTime',new Date().getTime());
                localStorage.removeItem('xpay_token')
                router.replace('/login'); 
            }).catch(action=> {   
                if(action === 'cancel'){
                    localStorage.setItem('confirmTime',new Date().getTime());
                    window.location.reload()
                } 
            });
        }  
    }
}

httpInstance.interceptors.request.use((config) => {
  const configs = config;
  if (localStorage.getItem('xpay_token')) {
    configs.headers.authorization = localStorage.getItem('xpay_token');
  }
  if(configs.url.indexOf('/login')>=0){
    delete configs.headers.authorization
  }
  // if()
    if(configs.method == 'post'){
      for (const key in configs.data) {
        // if(configs.data[key] === ""){
        //     delete configs.data[key];
        // }
      };
      //注意点 : post请求有两种方式，一种要body包装，一种不要body包装，使用方式参考 accountMgr.vue
      //使用序列化目的不让axios自动包装一层body上传 默认需要包上body postType:1
      if(configs.postType == 3){
        configs.headers['Content-Type'] =  'multipart/form-data';
      }else if(configs.postType == 2){
        configs.data = Qs.stringify(config.data);
        configs.headers['Content-Type'] =  'application/x-www-form-urlencoded';
      }else{
        configs.headers['Content-Type'] =  'application/json';
      }
    }else{
      for (const key in configs.params) {
        // if(configs.params[key] === ""){
        //     delete configs.params[key];
        // }
      };
      configs.headers['Content-Type'] =  'application/json';
    }
  if(!configs.hideLoading){
    showFullScreenLoading();
  }
  return configs;
}, error => Promise.reject(error));

httpInstance.interceptors.response.use((response) => {
  tryHideFullScreenLoading()

  const { status, data } = response;
  const { code, result } = data;
  
  if (status === 200) {
    if (code === 1) {
      if(result instanceof Array){
        if(!result[0]){
          return []
        }else{
          return result
        }
      }else{
        // let arr = Object.keys(result);
        if(result || result == 0){
          return result
        }else{
          return {}
        }
      }
    }else{
      if (code === -2) {
        router.push({name: 'login'})
      }
    }
    responseErrorHandler(response);
  }
  return Promise.reject(response);
}, (error) => {
  tryHideFullScreenLoading()
  responseErrorHandler(error);
  return Promise.reject(error);
});

export const $http = {
  get: (url, params = null, config = {}) => {
    const normalizedParams = params;
    return httpInstance.get(url, {
      params: normalizedParams,
      ...config,
    });
  },
  post: (url, data = null,config = {}) => httpInstance.post(url, data, config),
  all: (req) => axios.all(req),
};
export default {
    install(Vue) {
      Object.defineProperty(Vue.prototype, '$http', {
        value: $http,
        writable: false,
      });
      Object.defineProperty(Vue.prototype, '$axios', {
        value: axios,
        writable: false,
      });
    },
};