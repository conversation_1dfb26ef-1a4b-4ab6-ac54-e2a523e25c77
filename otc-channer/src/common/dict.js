import Vue from 'vue'


function returnLabel(val,arr) {
    var length = arr.length;
    for (var k = 0 ; k  < length ; k++) {
        if(arr[k].value == val){

            return arr[k].label
        }
    }
    return val ? val : '无';
}
// 货币转换 保留两位小数 ********.45 => 21,787,848.46
Vue.filter('moneyFormate',function(money){
    var accounting = require('accounting');
    return accounting.formatNumber(money,2,",");
});
//货币转换 ********.4578 => 21,787,848.4578
Vue.filter('toCurrency',function(number){
    var number = number ? number+'' : '0.00';
    var l = number.split('.')[0].split("").reverse(),
        r = number.indexOf('.') > 0 ? "." + number.split('.')[1] : '',
        t = "";
    for(var i = 0;i < l.length;i++){
        t += l[i] + ((i + 1) % 3 == 0 && (i + 1) != l.length ? ',' : '')
    }
    return t.split("").reverse().join("") + r
});
//货币格式转换 '*********' => '189,543,247'
Vue.filter('toMoneny',function(val){
    reg = /(?=(\B)(\d{3})+$)/g;
    return val.replace(reg, ',')
})
//显示星号 '***********' => '153****6203'
Vue.filter('hidePhone',function(val){
    var reg = /1(\d{2})\d{4}(\d{4})/g;
    return val.replace(reg,"1$1****$2");
});
//转换日期格式yyyy-mm-dd ************* => 2020-03-27
Vue.filter('formatedate',function(val){
    if(val){
        let moment=require('moment');
        return moment(val).format("YYYY-MM-DD HH:mm:ss");
        formatedate
    }else{
        return ''
    }
});
//利率格式化 0.4578 => 45.78
Vue.filter('formatexpect',function(val){
    return val ? parseFloat(val * 100).toFixed(2) : '0.00';
});
//利率格式 0.4578 => 45.78%
Vue.filter('rateFormat',function(val){
    return val ? parseFloat(val * 100).toFixed(2) + '%' : '0.00%';
});
//计算 1+  2-  3*  4/
Vue.filter('calcMethods',function(val1,val2,mt,to){
    let Big = require('big.js');
    let x = new Big(val1);
    switch(mt){
        case 1 : return x.plus(val2).toFixed(to ? to : '');break;
        case 2 : return x.minus(val2).toFixed(to ? to : '');break;
        case 3 : return x.times(val2).toFixed(to ? to : '');break;
        case 4 : return x.div(val2).toFixed(to ? to : '');break;
        default : return x
    }
});
// 保留小数点后四位
Vue.filter('Fslice', function(val){
    if(!val) return '0.0000'
    let strArr = String(val).split('.')
    if(strArr[1]){
        return `${strArr[0]}.${strArr[1].slice(0,4)}`
    }else{
        return `${strArr[0]}.0000`
    }
})
// 保留小数点后两位
Vue.filter('Tslice', function(val){
    if(!val) return '0.00'
    let strArr = String(val).split('.')
    if(strArr[1]){
        return `${strArr[0]}.${strArr[1].slice(0,2)}`
    }else{
        return `${strArr[0]}.00`
    }
})
//对 无的过滤
Vue.filter('isNoText',function(val){
    return val === '无' ? "" : val;
});
Vue.filter('isText',function(val){
    return val === '无' ? '' : val;
});

// 银行卡
Vue.filter('BANKCARD_TYPE', function(val){
    return returnLabel(val,_arrList.payTypeList);
});

// 订单状态
Vue.filter('ORDERSTATUS',function(val){
    return returnLabel(val,_arrList.orderStatus);
});