import Vue from 'vue'
import Router from 'vue-router'
import MainRouter from './router'
// import store from '../store/store'

Vue.use(Router)
const router = new Router({
  // mode: 'history',
  base: process.env.BASE_URL,
  routes: [
    ...MainRouter
  ]
});


//路由拦截
router.beforeEach((to, from, next) => {
  document.title = to.meta.title ? to.meta.title : '渠道管理后台';
  next()
  // if (!to.meta.auth) {
  //   next()
  // }else{
  //   if(sessionStorage.getItem('xpay_token')){
  //     next()
  //   }else{
  //     next({ path: '/login' })
  //   }
  // }
})
export default router;