import Login from '@/pages/login'
import IndexPage from '@/pages/home'
import Home from '@/pages/home/<USER>'
import ErrorPage from '@/pages/404'


  
import Myasset from '@/pages/myasset/index'
import LiftCoin from '@/pages/liftCoin/index'
import Earnings from '@/pages/earnings/index'

import AccountInfo from '@/pages/accountInfo/index' 
import playerList from '@/pages/playerList/index' 
import balanceMove from '@/pages/balanceMove/index' 
import payOrderList from '@/pages/payOrderList/index' 
import collectionRecord from '@/pages/collectionRecord/index'
import agentRelation from '@/pages/agentRelation/index'

import transferRecord from '@/pages/transferRecord/index'

const mainMap = [
    {
        path: '/',
        name: 'Index',
        redirect: '/login',
        meta: { auth: false },
        component: IndexPage
    },
    {
        path: '/login',
        name: 'login',
        meta: { auth: false},
        component: Login
    },
    {
        path: '/index',
        redirect: '/home',
        meta: { auth: true},
        component: IndexPage,
        children: [
            {
                path: "/home",
                name: 'Home',
                component: Home,
                meta: { auth: true},
            },
             
            {
                path: "/myasset",
                name: 'Myasset',
                component: Myasset,
                meta: { auth: true},
            },
            {
                path: "/liftCoin",
                name: 'LiftCoin',
                component: LiftCoin,
                meta: { auth: true},
            },
            {
                path: "/Earnings",
                name: 'Earnings',
                component: Earnings,
                meta: { auth: true},
            },
            {
                path: "/accountInfo",
                name: 'accountInfo',
                component: AccountInfo,
                meta: { auth: true},
            },
            {
                path: "/playerList",
                name: 'playerList',
                component: playerList,
                meta: { auth: true},
            },
            {
                path: "/balanceMove",
                name: 'balanceMove',
                component: balanceMove,
                meta: { auth: true},
            }, 
            /* {
              path: "/agentRelation",
              name: 'agentRelation',
              component: agentRelation,
              meta: { auth: true},
            },  */
            {
                path: "/payOrderList",
                name: 'payOrderList',
                component: payOrderList,
                meta: { auth: true},
            }, 
            {
                path: "/collectionRecord",
                name: 'collectionRecord',
                component: collectionRecord,
                meta: { auth: true},
            },
            /* {
              path: "/transferRecord",
              name: 'transferRecord',
              component: transferRecord,
              meta: { auth: true},
            },  */
            
            
        ]
    },
    {
        path: '/404',
        name: 'error',
        meta: { auth: false},
        component: ErrorPage
    },
    {
        path: '*',
        name: 'error',
        meta: { auth: false},
        component: ErrorPage
    },

]
 export default [
     ...mainMap
 ]