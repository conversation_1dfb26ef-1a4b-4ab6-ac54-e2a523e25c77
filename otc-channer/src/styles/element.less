
.router-view{
    display: flex;
    flex-flow: column;
}
.tableList{
    width: 100%;
    padding: 0px 30px 0;
    flex: 1;
    // overflow-y: scroll;
    // display: flex; align-items: center;
    // flex-flow: column;
    .el-table td, .el-table th{
        padding: 6px 2px;
    }
}
.el-dialog__header{
    text-align: center;
    background: #4d87f3;
    padding-top: 10px!important;
}
.el-dialog__title{
    color: #fff!important;
    font-size: 16px!important;
}
.el-dialog__headerbtn{
    top:15px!important;
}
.el-dialog__headerbtn .el-dialog__close{
    color: #fff!important;
}
.el-dialog__body{
    // padding-right: 60px!important;
}
.el-dialog__body{
    .el-input__inner{
        background:#EFF2F6!important;
        // border: none;
    }
    .el-textarea__inner{
        background: #EFF2F6!important;
    }
    .el-form-item__label{
        // color: #808080!important;
        font-size: 14px;
    }
    .el-form-item{
        margin-bottom: 0px!important;
    }
    // .el-form-item__content{
    //     padding-left: 10px;
    // }
    .el-textarea__inner:focus{
        // border: none!important;
    }
    
}
.el-dialog__footer{
    text-align: center!important;
    .el-button{
        padding: 10px 20px!important;
        width: 130px!important;
        font-size: 14px;
    }
}


.searchHeader{
    width: 100%;
    min-height: 60px;
    padding: 20px 15px 10px 30px;
    overflow: hidden;
    .el-input__inner{
        // background:#EFF2F6!important;
        // border: none;
    }
    .el-range-editor--small .el-range-input{
        // background:#EFF2F6!important;
    }
    .el-form--inline .el-form-item__label{
        // width:90px;
        font-size: 12px;
    }
    .el-form--inline .el-form-item{
        margin-bottom: 10px;
        margin-right: 20px;
        vertical-align: inherit;
    }
   
}


.srcImg{
    .el-form-item__content{
      display: flex;
      .el-input{
          flex: 1;
      }
      img{
          width: 120px;
          height: 38px;margin: 1px 0 1px 10px;
      }
    }
}

.c-page-info{
    padding:0 0; width: 100%;
    display: flex; font-size: 12px;
    align-items: center;
    justify-content: space-between;
    .info-item{
      display: inline-block;
      margin-right: 30px;
      span{color: #999;}
      p{line-height: 2em;}
    }
  }
  .container{
    background-color: red;
}