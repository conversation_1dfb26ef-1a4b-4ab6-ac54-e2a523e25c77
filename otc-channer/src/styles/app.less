html, body {
  height: 100%;
  width: 100%;
  overflow-x: hidden;
  margin: 0;
}
#app {
  height: 100%;
  width: 100%;
  font-family: "PingFang SC","Helvetica Neue",Helvetica,"Hiragino Sans GB","Microsoft YaHei","微软雅黑",Arial,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  color: #333;font-size: 14px;
}
*{
  padding: 0;
  margin: 0;
  box-sizing: border-box;
}
ul,ol,li{
  list-style: none;
}
input{
  border: none;
  outline: none;
}
i{
  font-style: normal;
}
.sys-box{
    height: 100%;
    .el-container {
        height: 100%;
        overflow: hidden;
    }
}

.container{
    padding: 30px !important;
    display: flex ;
    flex-direction: column;
    justify-content: space-between;
    align-items: center;
    box-sizing: border-box;
    height: 100%;
    .el-collapse{
        width: 100%; 
        border-top-color: transparent; 
        .el-collapse-item__content {
            padding-bottom: 10px;
        }
    }
    .el-table thead {
        color: #333;
        font-weight: 500;
    }
    .footer{
        width: 100%;
        display: flex;
        flex-direction: row;
        justify-content: space-between;
        align-items: center;
        box-sizing: border-box;
        padding:  0 ;
    }
}