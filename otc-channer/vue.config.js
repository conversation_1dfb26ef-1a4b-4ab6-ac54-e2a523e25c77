// const CopyWebpackPlugin = require('copy-webpack-plugin');//复制
// const BundleAnalyzerPlugin = require('webpack-bundle-analyzer').BundleAnalyzerPlugin;//打包分析
const CompressionPlugin = require('compression-webpack-plugin');//引入gzip压缩插件

module.exports = {
     publicPath:'./',
    // outputDir: `dist/channel`,
    
    assetsDir:'',
    lintOnSave:false,
    productionSourceMap:false,
    configureWebpack:config => {
        if(process.env.NODE_ENV === 'production'){
            return {
                plugins: [
                    // 复制
                    // new CopyWebpackPlugin([
                    //     {
                    //         from: 'public/',
                    //         to: 'dist',
                    //         ignore: ['.*']
                    //     }
                    // ]),
                    // 代码压缩
                    new CompressionPlugin({
                        test: /\.js$|\.html$|\.css/,
                        threshold: 10240,
                        deleteOriginalAssets: false
                    }),
                    // 打包分析
                    // new BundleAnalyzerPlugin()
                ],
                externals: {
                    xlsx: 'xlsx',
                    echarts: 'echarts',
                },
            }
        }
    },
    devServer: {
        host: '0.0.0.0',
        port: 8092,
        open: true,
        // proxy: {
            // '/agent': {
            //     // target: 'http://speed123.xicp.net/', 
            //     target: 'http://**************:6300/', 
            //     // changeOrigin: true,
            //     // pathRewrite: {"/test" : ""}
            // },            
        // }
    },
}