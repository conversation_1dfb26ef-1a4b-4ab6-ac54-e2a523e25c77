{"name": "manager", "version": "0.1.0", "private": true, "scripts": {"serve": "vue-cli-service serve", "build": "vue-cli-service build", "lint": "vue-cli-service lint", "dev": "vue-cli-service serve"}, "dependencies": {"accounting": "^0.4.1", "axios": "^0.19.2", "core-js": "^3.4.4", "echarts": "^4.6.0", "element-ui": "^2.13.0", "file-saver": "^2.0.2", "js-md5": "^0.7.3", "lodash": "^4.17.19", "moment": "^2.24.0", "qs": "^6.9.1", "quill": "^1.3.7", "v-viewer": "^1.5.1", "vue": "^2.6.10", "vue-clipboard2": "^0.3.1", "vue-quill-editor": "^3.0.6", "vue-router": "^3.1.3", "vuex": "^3.1.2", "xlsx": "^0.15.4"}, "devDependencies": {"@vue/cli-plugin-babel": "^4.1.0", "@vue/cli-plugin-eslint": "^4.1.0", "@vue/cli-service": "^4.1.0", "babel-eslint": "^10.0.3", "compression-webpack-plugin": "^4.0.0", "eslint": "^5.16.0", "eslint-plugin-vue": "^5.0.0", "less": "^3.10.3", "less-loader": "^5.0.0", "script-loader": "^0.7.2", "vue-template-compiler": "^2.6.10"}, "eslintConfig": {"root": true, "env": {"node": true}, "extends": ["plugin:vue/essential", "eslint:recommended"], "rules": {}, "parserOptions": {"parser": "babel-es<PERSON>"}}, "browserslist": ["> 1%", "last 2 versions"]}